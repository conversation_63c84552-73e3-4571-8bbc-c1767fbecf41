#!/usr/bin/env python3
"""
Main execution script for quantum annealing parking lot optimization.

This script provides a command-line interface for running the parking lot
layout optimization using quantum annealing.
"""

import argparse
import sys
import os
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.config import ParkingLotConfig
from src.preprocessing.module_generator import ModuleGenerator
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer
from src.solver.postprocessor import SolutionPostProcessor
from src.visualization.layout_plotter import LayoutPlotter
from src.visualization.metrics_analyzer import MetricsAnalyzer
from src.utils.helpers import (
    setup_logging, print_solution_summary, save_solution_to_file,
    timer, memory_usage, calculate_parking_lot_statistics
)
import matplotlib.pyplot as plt


@timer
def run_optimization(config_path: str = None, 
                    output_dir: str = "output",
                    show_plots: bool = True,
                    save_plots: bool = True,
                    verbose: bool = False) -> dict:
    """
    Run the complete parking lot optimization.
    
    Args:
        config_path: Path to configuration file (uses default if None)
        output_dir: Directory to save outputs
        show_plots: Whether to display plots
        save_plots: Whether to save plots to files
        verbose: Enable verbose logging
        
    Returns:
        Dictionary with optimization results
    """
    # Set up logging
    log_level = "DEBUG" if verbose else "INFO"
    logger = setup_logging(log_level)
    
    logger.info("Starting quantum annealing parking lot optimization")
    logger.info(f"Memory usage: {memory_usage()['rss_mb']:.1f} MB")
    
    # Load configuration
    if config_path:
        logger.info(f"Loading configuration from {config_path}")
        config = ParkingLotConfig.from_yaml(config_path)
    else:
        logger.info("Using default configuration")
        config = ParkingLotConfig(
            width=50.0,
            height=30.0,
            obstacles=[
                (10.0, 10.0, 2.0, 2.0),
                (25.0, 15.0, 1.5, 1.5),
                (40.0, 8.0, 2.0, 2.0)
            ]
        )
    
    # Validate configuration
    validation_errors = config.validate()
    if validation_errors:
        logger.error("Configuration validation failed:")
        for error in validation_errors:
            logger.error(f"  - {error}")
        return {"error": "Invalid configuration"}
    
    # Print parking lot statistics
    stats = calculate_parking_lot_statistics(config)
    logger.info(f"Parking lot: {config.width}m x {config.height}m")
    logger.info(f"Total area: {stats['total_area_m2']:.1f} m²")
    logger.info(f"Obstacles: {stats['num_obstacles']} ({stats['obstacle_ratio']:.1%} coverage)")
    logger.info(f"Max theoretical parking spaces: {stats['max_theoretical_parking_spaces']}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Phase 1: Generate candidate modules
    logger.info("Phase 1: Generating candidate road modules...")
    generator = ModuleGenerator(config)
    modules = generator.generate_candidate_modules()
    
    module_stats = generator.get_module_statistics()
    logger.info(f"Generated {module_stats['total_modules']} candidate modules")
    logger.info(f"  - Single lane: {module_stats['single_lane_modules']}")
    logger.info(f"  - Double lane: {module_stats['double_lane_modules']}")
    logger.info(f"  - Turning areas: {module_stats['turning_area_modules']}")
    logger.info(f"Average accessibility: {module_stats['average_accessibility']:.2f}")
    
    if len(modules) == 0:
        logger.error("No candidate modules generated!")
        return {"error": "No candidate modules"}
    
    # Phase 2: Formulate QUBO problem
    logger.info("Phase 2: Formulating QUBO problem...")
    formulator = QUBOFormulator(modules, config)
    qubo_matrix = formulator.build_qubo_matrix()
    
    qubo_analysis = formulator.analyze_qubo_structure()
    logger.info(f"QUBO matrix: {qubo_analysis['num_variables']} variables, "
               f"{qubo_analysis['total_terms']} terms")
    logger.info(f"Matrix density: {qubo_analysis['matrix_density']:.3f}")
    
    # Check for formulation issues
    formulation_issues = formulator.validate_qubo_formulation()
    if formulation_issues:
        logger.warning("QUBO formulation issues detected:")
        for issue in formulation_issues:
            logger.warning(f"  - {issue}")
    
    # Phase 3: Solve with quantum annealing
    logger.info("Phase 3: Solving with quantum annealing...")
    solver = QuantumAnnealer(config)
    
    # Adjust number of reads based on problem size
    num_reads = min(2000, max(1000, qubo_analysis['num_variables'] * 10))
    logger.info(f"Using {num_reads} annealing reads")
    
    solution_result = solver.solve_with_formulator(formulator, num_reads=num_reads)
    
    logger.info(f"Best energy: {solution_result['energy']:.2f}")
    logger.info(f"Solver: {solution_result['solver_info']['sampler_type']}")
    
    # Analyze solution quality
    quality_analysis = solver.analyze_solution_quality()
    logger.info(f"Success rate: {quality_analysis['success_rate']:.1%}")
    logger.info(f"Unique solutions found: {quality_analysis['unique_solutions']}")
    
    # Post-process solution
    logger.info("Post-processing solution...")
    postprocessor = SolutionPostProcessor(modules, config)
    processed_solution = postprocessor.process_solution(solution_result['solution'])
    
    # Print solution summary
    print_solution_summary(processed_solution)
    
    # Detailed analysis
    validation = processed_solution['validation']
    metrics = processed_solution['performance_metrics']
    network = processed_solution['network_analysis']
    
    logger.info(f"Solution validation: {'VALID' if validation['is_valid'] else 'INVALID'}")
    logger.info(f"Selected modules: {len(processed_solution['selected_modules'])}")
    logger.info(f"Total parking spaces: {metrics['total_parking_spaces']:.0f}")
    logger.info(f"Road area: {metrics['total_road_area']:.2f} m²")
    logger.info(f"Area efficiency: {metrics['area_efficiency']:.3f} spaces/m²")
    logger.info(f"Connected components: {network['num_components']}")
    
    if validation['overlap_violations']:
        logger.warning(f"Overlap violations: {len(validation['overlap_violations'])}")
    
    # Create visualizations
    logger.info("Creating visualizations...")
    plotter = LayoutPlotter(config)
    analyzer = MetricsAnalyzer(config)
    
    # Main solution plot
    fig1, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Base parking lot
    plotter.plot_parking_lot_base(ax=axes[0,0])
    axes[0,0].set_title("Parking Lot with Obstacles")
    
    # Sample candidate modules
    sample_modules = modules[:30] if len(modules) > 30 else modules
    plotter.plot_candidate_modules(sample_modules, ax=axes[0,1])
    axes[0,1].set_title("Sample Candidate Modules")
    
    # Final solution
    plotter.plot_solution(
        processed_solution['selected_modules'],
        all_modules=modules,
        ax=axes[1,0],
        show_accessibility=True
    )
    axes[1,0].set_title("Optimized Road Layout")
    
    # Module accessibility heatmap
    plotter.plot_module_heatmap(
        modules,
        value_key='parking_accessibility_value',
        ax=axes[1,1]
    )
    axes[1,1].set_title("Parking Accessibility Heatmap")
    
    plt.tight_layout()
    
    # Performance metrics
    fig2 = analyzer.plot_solution_metrics([processed_solution])
    
    # QUBO analysis
    fig3 = analyzer.plot_qubo_analysis(qubo_analysis)
    
    # Save plots
    if save_plots:
        plotter.save_plot(fig1, os.path.join(output_dir, "optimization_results.png"))
        plotter.save_plot(fig2, os.path.join(output_dir, "performance_metrics.png"))
        plotter.save_plot(fig3, os.path.join(output_dir, "qubo_analysis.png"))
        logger.info(f"Plots saved to {output_dir}/")
    
    # Save solution data
    save_solution_to_file(processed_solution, os.path.join(output_dir, "solution.json"))
    
    # Save configuration for reproducibility
    config.to_yaml(os.path.join(output_dir, "config.yaml"))
    
    # Show plots
    if show_plots:
        plt.show()
    else:
        plt.close('all')
    
    logger.info(f"Final memory usage: {memory_usage()['rss_mb']:.1f} MB")
    logger.info("Optimization completed successfully!")
    
    return {
        "config": config,
        "modules": modules,
        "solution": processed_solution,
        "qubo_analysis": qubo_analysis,
        "quality_analysis": quality_analysis
    }


def main():
    """Main command-line interface."""
    parser = argparse.ArgumentParser(
        description="Quantum Annealing Parking Lot Layout Optimization",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                                    # Run with default configuration
  python main.py --config config/custom.yaml       # Use custom configuration
  python main.py --output results/ --no-show       # Save to results/, don't show plots
  python main.py --verbose                         # Enable verbose logging
        """
    )
    
    parser.add_argument(
        "--config", "-c",
        type=str,
        help="Path to configuration YAML file"
    )
    
    parser.add_argument(
        "--output", "-o",
        type=str,
        default="output",
        help="Output directory for results (default: output)"
    )
    
    parser.add_argument(
        "--no-show",
        action="store_true",
        help="Don't display plots (only save them)"
    )
    
    parser.add_argument(
        "--no-save",
        action="store_true",
        help="Don't save plots to files"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    args = parser.parse_args()
    
    try:
        results = run_optimization(
            config_path=args.config,
            output_dir=args.output,
            show_plots=not args.no_show,
            save_plots=not args.no_save,
            verbose=args.verbose
        )
        
        if "error" in results:
            print(f"Error: {results['error']}")
            sys.exit(1)
        
        print("\nOptimization completed successfully!")
        print(f"Results saved to: {args.output}/")
        
    except KeyboardInterrupt:
        print("\nOptimization interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error during optimization: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
