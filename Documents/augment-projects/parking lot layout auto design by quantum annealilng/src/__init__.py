"""
Quantum Annealing Parking Lot Layout Optimization

A module-based QUBO approach for optimizing underground parking lot layouts
to maximize parking spaces while accommodating structural constraints.
"""

__version__ = "1.0.0"
__author__ = "Quantum Parking Optimization Team"
__email__ = "<EMAIL>"

from .core.config import ParkingLotConfig
from .preprocessing.module_generator import ModuleGenerator
from .qubo.formulation import QUBOFormulator
from .solver.quantum_annealer import QuantumAnnealer

__all__ = [
    "ParkingLotConfig",
    "ModuleGenerator", 
    "QUBOFormulator",
    "QuantumAnnealer"
]
