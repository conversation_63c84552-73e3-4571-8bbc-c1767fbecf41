"""
Geometric utilities and data structures for parking lot optimization.

This module provides fundamental geometric classes and utilities for
handling coordinates, rectangles, and spatial operations.
"""

from dataclasses import dataclass
from typing import List, Tuple, Set, Optional
import numpy as np


@dataclass(frozen=True)
class Point:
    """Represents a 2D point with x, y coordinates."""
    x: float
    y: float
    
    def __add__(self, other: 'Point') -> 'Point':
        """Add two points."""
        return Point(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other: 'Point') -> 'Point':
        """Subtract two points."""
        return Point(self.x - other.x, self.y - other.y)
    
    def distance_to(self, other: 'Point') -> float:
        """Calculate Euclidean distance to another point."""
        return np.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


@dataclass(frozen=True)
class Rectangle:
    """Represents a rectangle with position and dimensions."""
    x: float  # Bottom-left x coordinate
    y: float  # Bottom-left y coordinate
    width: float
    height: float
    
    @property
    def left(self) -> float:
        """Left edge x-coordinate."""
        return self.x
    
    @property
    def right(self) -> float:
        """Right edge x-coordinate."""
        return self.x + self.width
    
    @property
    def bottom(self) -> float:
        """Bottom edge y-coordinate."""
        return self.y
    
    @property
    def top(self) -> float:
        """Top edge y-coordinate."""
        return self.y + self.height
    
    @property
    def center(self) -> Point:
        """Center point of the rectangle."""
        return Point(self.x + self.width / 2, self.y + self.height / 2)
    
    @property
    def area(self) -> float:
        """Area of the rectangle."""
        return self.width * self.height
    
    @property
    def corners(self) -> List[Point]:
        """Get all four corner points of the rectangle."""
        return [
            Point(self.left, self.bottom),   # Bottom-left
            Point(self.right, self.bottom),  # Bottom-right
            Point(self.right, self.top),     # Top-right
            Point(self.left, self.top)       # Top-left
        ]
    
    def contains_point(self, point: Point) -> bool:
        """Check if a point is inside the rectangle."""
        return (self.left <= point.x <= self.right and 
                self.bottom <= point.y <= self.top)
    
    def overlaps_with(self, other: 'Rectangle') -> bool:
        """Check if this rectangle overlaps with another rectangle."""
        return not (self.right <= other.left or 
                   other.right <= self.left or
                   self.top <= other.bottom or 
                   other.top <= self.bottom)
    
    def intersection_with(self, other: 'Rectangle') -> Optional['Rectangle']:
        """
        Calculate the intersection rectangle with another rectangle.
        
        Returns:
            Rectangle representing the intersection, or None if no intersection
        """
        if not self.overlaps_with(other):
            return None
            
        left = max(self.left, other.left)
        right = min(self.right, other.right)
        bottom = max(self.bottom, other.bottom)
        top = min(self.top, other.top)
        
        return Rectangle(left, bottom, right - left, top - bottom)
    
    def union_with(self, other: 'Rectangle') -> 'Rectangle':
        """Calculate the bounding rectangle that contains both rectangles."""
        left = min(self.left, other.left)
        right = max(self.right, other.right)
        bottom = min(self.bottom, other.bottom)
        top = max(self.top, other.top)
        
        return Rectangle(left, bottom, right - left, top - bottom)
    
    def expand(self, margin: float) -> 'Rectangle':
        """Expand the rectangle by a given margin on all sides."""
        return Rectangle(
            self.x - margin,
            self.y - margin,
            self.width + 2 * margin,
            self.height + 2 * margin
        )
    
    def to_grid_cells(self, cell_size: float) -> Set[Tuple[int, int]]:
        """
        Convert rectangle to a set of grid cell coordinates.
        
        Args:
            cell_size: Size of each grid cell
            
        Returns:
            Set of (row, col) tuples representing covered grid cells
        """
        cells = set()
        
        # Calculate grid bounds
        left_cell = int(self.left / cell_size)
        right_cell = int(np.ceil(self.right / cell_size))
        bottom_cell = int(self.bottom / cell_size)
        top_cell = int(np.ceil(self.top / cell_size))
        
        # Add all cells that intersect with the rectangle
        for row in range(bottom_cell, top_cell):
            for col in range(left_cell, right_cell):
                cell_rect = Rectangle(
                    col * cell_size,
                    row * cell_size,
                    cell_size,
                    cell_size
                )
                if self.overlaps_with(cell_rect):
                    cells.add((row, col))
                    
        return cells


@dataclass(frozen=True)
class CarDimensions:
    """Standard car dimensions and spacing requirements."""
    width: float = 2.5  # meters
    length: float = 5.0  # meters
    clearance: float = 0.3  # additional clearance around car
    
    @property
    def total_width(self) -> float:
        """Total width including clearance."""
        return self.width + 2 * self.clearance
    
    @property
    def total_length(self) -> float:
        """Total length including clearance."""
        return self.length + 2 * self.clearance
    
    def get_parking_space_rectangle(self, x: float, y: float, 
                                  orientation: str = 'horizontal') -> Rectangle:
        """
        Get a rectangle representing a parking space at the given position.
        
        Args:
            x, y: Position of the parking space
            orientation: 'horizontal' or 'vertical'
            
        Returns:
            Rectangle representing the parking space
        """
        if orientation == 'horizontal':
            return Rectangle(x, y, self.total_length, self.total_width)
        else:  # vertical
            return Rectangle(x, y, self.total_width, self.total_length)


def calculate_parking_spaces_around_module(module_rect: Rectangle, 
                                         car_dims: CarDimensions,
                                         parking_lot_bounds: Rectangle) -> List[Rectangle]:
    """
    Calculate potential parking spaces around a road module.
    
    Args:
        module_rect: Rectangle representing the road module
        car_dims: Car dimensions for parking space calculation
        parking_lot_bounds: Bounds of the parking lot
        
    Returns:
        List of Rectangle objects representing potential parking spaces
    """
    parking_spaces = []
    
    # Try parking spaces on all four sides of the module
    sides = [
        ('left', module_rect.left - car_dims.total_length, module_rect.y, 
         car_dims.total_length, module_rect.height, 'horizontal'),
        ('right', module_rect.right, module_rect.y,
         car_dims.total_length, module_rect.height, 'horizontal'),
        ('bottom', module_rect.x, module_rect.bottom - car_dims.total_width,
         module_rect.width, car_dims.total_width, 'vertical'),
        ('top', module_rect.x, module_rect.top,
         module_rect.width, car_dims.total_width, 'vertical')
    ]
    
    for side_name, x, y, width, height, orientation in sides:
        # Create potential parking area
        parking_area = Rectangle(x, y, width, height)
        
        # Check if parking area is within parking lot bounds
        if (parking_area.left >= parking_lot_bounds.left and
            parking_area.right <= parking_lot_bounds.right and
            parking_area.bottom >= parking_lot_bounds.bottom and
            parking_area.top <= parking_lot_bounds.top):
            
            # Divide parking area into individual parking spaces
            if orientation == 'horizontal':
                # Parking spaces along the length
                num_spaces = int(parking_area.height / car_dims.total_width)
                for i in range(num_spaces):
                    space_y = parking_area.y + i * car_dims.total_width
                    space = Rectangle(parking_area.x, space_y, 
                                    parking_area.width, car_dims.total_width)
                    parking_spaces.append(space)
            else:  # vertical
                # Parking spaces along the width
                num_spaces = int(parking_area.width / car_dims.total_length)
                for i in range(num_spaces):
                    space_x = parking_area.x + i * car_dims.total_length
                    space = Rectangle(space_x, parking_area.y,
                                    car_dims.total_length, parking_area.height)
                    parking_spaces.append(space)
    
    return parking_spaces


def check_rectangles_overlap(rect1: Rectangle, rect2: Rectangle) -> bool:
    """
    Check if two rectangles overlap.
    
    Args:
        rect1, rect2: Rectangles to check
        
    Returns:
        True if rectangles overlap, False otherwise
    """
    return rect1.overlaps_with(rect2)


def calculate_rectangle_overlap_area(rect1: Rectangle, rect2: Rectangle) -> float:
    """
    Calculate the area of overlap between two rectangles.
    
    Args:
        rect1, rect2: Rectangles to check
        
    Returns:
        Area of overlap (0 if no overlap)
    """
    intersection = rect1.intersection_with(rect2)
    return intersection.area if intersection else 0.0
