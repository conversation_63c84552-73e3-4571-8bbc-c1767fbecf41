"""
Grid management for parking lot discretization.

This module provides utilities for managing the grid-based representation
of the parking lot and converting between continuous and discrete coordinates.
"""

from typing import List, Tuple, Set, Optional
import numpy as np
from .geometry import Point, Rectangle
from .config import ParkingLotConfig


class GridManager:
    """
    Manages the grid-based discretization of the parking lot.
    
    The grid divides the continuous parking lot space into discrete cells
    for easier computation and module placement.
    """
    
    def __init__(self, config: ParkingLotConfig):
        """
        Initialize the grid manager.
        
        Args:
            config: Parking lot configuration
        """
        self.config = config
        self.cell_size = config.grid.cell_size
        self.width_cells = int(config.width / self.cell_size)
        self.height_cells = int(config.height / self.cell_size)
        
        # Create obstacle mask
        self.obstacle_mask = self._create_obstacle_mask()
    
    def _create_obstacle_mask(self) -> np.ndarray:
        """
        Create a boolean mask indicating which grid cells contain obstacles.
        
        Returns:
            2D boolean array where True indicates obstacle cells
        """
        mask = np.zeros((self.height_cells, self.width_cells), dtype=bool)
        
        for x, y, width, height in self.config.obstacles:
            obstacle_rect = Rectangle(x, y, width, height)
            obstacle_cells = obstacle_rect.to_grid_cells(self.cell_size)
            
            for row, col in obstacle_cells:
                if 0 <= row < self.height_cells and 0 <= col < self.width_cells:
                    mask[row, col] = True
                    
        return mask
    
    def continuous_to_grid(self, x: float, y: float) -> Tuple[int, int]:
        """
        Convert continuous coordinates to grid cell indices.
        
        Args:
            x, y: Continuous coordinates in meters
            
        Returns:
            Tuple of (row, col) grid indices
        """
        col = int(x / self.cell_size)
        row = int(y / self.cell_size)
        return row, col
    
    def grid_to_continuous(self, row: int, col: int) -> Tuple[float, float]:
        """
        Convert grid cell indices to continuous coordinates.
        
        Args:
            row, col: Grid cell indices
            
        Returns:
            Tuple of (x, y) continuous coordinates (bottom-left of cell)
        """
        x = col * self.cell_size
        y = row * self.cell_size
        return x, y
    
    def get_cell_center(self, row: int, col: int) -> Point:
        """
        Get the center point of a grid cell.
        
        Args:
            row, col: Grid cell indices
            
        Returns:
            Point representing the cell center
        """
        x, y = self.grid_to_continuous(row, col)
        return Point(x + self.cell_size / 2, y + self.cell_size / 2)
    
    def get_cell_rectangle(self, row: int, col: int) -> Rectangle:
        """
        Get the rectangle representing a grid cell.
        
        Args:
            row, col: Grid cell indices
            
        Returns:
            Rectangle representing the grid cell
        """
        x, y = self.grid_to_continuous(row, col)
        return Rectangle(x, y, self.cell_size, self.cell_size)
    
    def is_valid_cell(self, row: int, col: int) -> bool:
        """
        Check if a grid cell is within bounds and not an obstacle.
        
        Args:
            row, col: Grid cell indices
            
        Returns:
            True if cell is valid (within bounds and not obstacle)
        """
        if not (0 <= row < self.height_cells and 0 <= col < self.width_cells):
            return False
        return not self.obstacle_mask[row, col]
    
    def get_available_cells(self) -> Set[Tuple[int, int]]:
        """
        Get all available (non-obstacle) grid cells.
        
        Returns:
            Set of (row, col) tuples for available cells
        """
        available_cells = set()
        for row in range(self.height_cells):
            for col in range(self.width_cells):
                if self.is_valid_cell(row, col):
                    available_cells.add((row, col))
        return available_cells
    
    def rectangle_to_grid_cells(self, rect: Rectangle) -> Set[Tuple[int, int]]:
        """
        Convert a rectangle to the set of grid cells it covers.
        
        Args:
            rect: Rectangle to convert
            
        Returns:
            Set of (row, col) tuples for covered cells
        """
        return rect.to_grid_cells(self.cell_size)
    
    def grid_cells_to_rectangle(self, cells: Set[Tuple[int, int]]) -> Optional[Rectangle]:
        """
        Convert a set of grid cells to their bounding rectangle.
        
        Args:
            cells: Set of (row, col) grid cell coordinates
            
        Returns:
            Rectangle bounding all cells, or None if cells is empty
        """
        if not cells:
            return None
            
        rows, cols = zip(*cells)
        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)
        
        x, y = self.grid_to_continuous(min_row, min_col)
        width = (max_col - min_col + 1) * self.cell_size
        height = (max_row - min_row + 1) * self.cell_size
        
        return Rectangle(x, y, width, height)
    
    def check_rectangle_validity(self, rect: Rectangle) -> bool:
        """
        Check if a rectangle can be placed without overlapping obstacles.
        
        Args:
            rect: Rectangle to check
            
        Returns:
            True if rectangle is valid (no obstacle overlap)
        """
        covered_cells = self.rectangle_to_grid_cells(rect)
        
        for row, col in covered_cells:
            if not self.is_valid_cell(row, col):
                return False
                
        return True
    
    def get_adjacent_cells(self, row: int, col: int, 
                          include_diagonal: bool = False) -> List[Tuple[int, int]]:
        """
        Get adjacent cells to a given cell.
        
        Args:
            row, col: Grid cell indices
            include_diagonal: Whether to include diagonal neighbors
            
        Returns:
            List of (row, col) tuples for adjacent cells
        """
        adjacent = []
        
        # Define neighbor offsets
        if include_diagonal:
            offsets = [(-1, -1), (-1, 0), (-1, 1), (0, -1), 
                      (0, 1), (1, -1), (1, 0), (1, 1)]
        else:
            offsets = [(-1, 0), (0, -1), (0, 1), (1, 0)]
        
        for dr, dc in offsets:
            new_row, new_col = row + dr, col + dc
            if self.is_valid_cell(new_row, new_col):
                adjacent.append((new_row, new_col))
                
        return adjacent
    
    def find_connected_components(self, cells: Set[Tuple[int, int]]) -> List[Set[Tuple[int, int]]]:
        """
        Find connected components in a set of grid cells.
        
        Args:
            cells: Set of grid cells to analyze
            
        Returns:
            List of sets, each representing a connected component
        """
        if not cells:
            return []
            
        unvisited = cells.copy()
        components = []
        
        while unvisited:
            # Start a new component
            start_cell = unvisited.pop()
            component = {start_cell}
            stack = [start_cell]
            
            # DFS to find all connected cells
            while stack:
                current = stack.pop()
                row, col = current
                
                # Check all adjacent cells
                for adj_row, adj_col in self.get_adjacent_cells(row, col):
                    adj_cell = (adj_row, adj_col)
                    if adj_cell in unvisited:
                        unvisited.remove(adj_cell)
                        component.add(adj_cell)
                        stack.append(adj_cell)
            
            components.append(component)
            
        return components
    
    def calculate_cell_distances(self, start_cells: Set[Tuple[int, int]], 
                               target_cells: Set[Tuple[int, int]]) -> float:
        """
        Calculate minimum distance between two sets of cells.
        
        Args:
            start_cells: First set of cells
            target_cells: Second set of cells
            
        Returns:
            Minimum distance in grid cells
        """
        if not start_cells or not target_cells:
            return float('inf')
            
        min_distance = float('inf')
        
        for start_row, start_col in start_cells:
            for target_row, target_col in target_cells:
                distance = abs(start_row - target_row) + abs(start_col - target_col)
                min_distance = min(min_distance, distance)
                
        return min_distance
    
    def get_grid_info(self) -> dict:
        """
        Get information about the grid configuration.
        
        Returns:
            Dictionary with grid information
        """
        total_cells = self.width_cells * self.height_cells
        obstacle_cells = np.sum(self.obstacle_mask)
        available_cells = total_cells - obstacle_cells
        
        return {
            'width_cells': self.width_cells,
            'height_cells': self.height_cells,
            'total_cells': total_cells,
            'obstacle_cells': int(obstacle_cells),
            'available_cells': int(available_cells),
            'cell_size': self.cell_size,
            'obstacle_ratio': obstacle_cells / total_cells
        }
