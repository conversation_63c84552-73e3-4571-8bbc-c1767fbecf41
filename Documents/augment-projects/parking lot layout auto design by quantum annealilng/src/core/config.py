"""
Configuration management for parking lot optimization.

This module handles loading and managing configuration parameters
for the quantum annealing parking lot layout optimization system.
"""

import yaml
from dataclasses import dataclass, field
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path


@dataclass
class CarDimensions:
    """Standard car dimensions and clearance requirements."""
    width: float = 2.5  # meters
    length: float = 5.0  # meters
    clearance: float = 0.3  # additional clearance around car


@dataclass
class GridConfig:
    """Grid configuration for discretizing the parking lot."""
    cell_size: float = 0.5  # meters per grid cell


@dataclass
class RoadModuleSpecs:
    """Specifications for different types of road modules."""
    single_lane_min_width: float = 2.5  # meters
    single_lane_max_width: float = 3.5  # meters
    double_lane_min_width: float = 5.0  # meters
    double_lane_max_width: float = 6.0  # meters
    turning_area_min_size: float = 5.0  # meters


@dataclass
class ModuleGenerationParams:
    """Parameters for generating candidate modules."""
    min_module_length: float = 5.0  # meters
    max_module_length: float = 20.0  # meters
    step_size: float = 0.5  # meters


@dataclass
class QUBOWeights:
    """Weight coefficients for QUBO objective function components."""
    overlap_penalty: float = 1000.0  # P_overlap
    coverage_reward: float = 10.0    # A
    connectivity_reward: float = 5.0  # B
    area_penalty: float = 1.0        # C


@dataclass
class QuantumAnnealingParams:
    """Parameters for quantum annealing solver."""
    num_reads: int = 1000
    annealing_time: int = 20  # microseconds
    chain_strength: float = 1.0
    auto_scale: bool = True


@dataclass
class SolverConfig:
    """Solver configuration options."""
    use_simulator: bool = True
    simulator_type: str = "neal"  # Options: "neal", "exact"


@dataclass
class VisualizationConfig:
    """Configuration for visualization and plotting."""
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 300
    colors: Dict[str, str] = field(default_factory=lambda: {
        "parking_space": "#E8F4FD",
        "road": "#666666", 
        "obstacle": "#FF6B6B",
        "selected_module": "#4ECDC4",
        "grid_lines": "#CCCCCC"
    })


@dataclass
class ValidationParams:
    """Parameters for solution validation."""
    check_overlap: bool = True
    check_connectivity: bool = True
    min_parking_spaces: int = 10
    max_road_ratio: float = 0.4


@dataclass
class ParkingLotConfig:
    """
    Main configuration class for parking lot optimization.
    
    This class encapsulates all configuration parameters needed for
    the quantum annealing parking lot layout optimization.
    """
    # Parking lot dimensions
    width: float  # meters
    height: float  # meters
    
    # Obstacles: List of (x, y, width, height) tuples
    obstacles: List[Tuple[float, float, float, float]] = field(default_factory=list)
    
    # Configuration components
    car: CarDimensions = field(default_factory=CarDimensions)
    grid: GridConfig = field(default_factory=GridConfig)
    road_modules: RoadModuleSpecs = field(default_factory=RoadModuleSpecs)
    module_generation: ModuleGenerationParams = field(default_factory=ModuleGenerationParams)
    qubo_weights: QUBOWeights = field(default_factory=QUBOWeights)
    quantum_annealing: QuantumAnnealingParams = field(default_factory=QuantumAnnealingParams)
    solver: SolverConfig = field(default_factory=SolverConfig)
    visualization: VisualizationConfig = field(default_factory=VisualizationConfig)
    validation: ValidationParams = field(default_factory=ValidationParams)
    
    @classmethod
    def from_yaml(cls, config_path: str) -> 'ParkingLotConfig':
        """
        Load configuration from a YAML file.
        
        Args:
            config_path: Path to the YAML configuration file
            
        Returns:
            ParkingLotConfig instance with loaded parameters
        """
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
            
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
            
        # Extract required parameters
        if 'parking_lot' not in config_data:
            raise ValueError("Configuration must include 'parking_lot' section")
            
        parking_lot_data = config_data['parking_lot']
        width = parking_lot_data['width']
        height = parking_lot_data['height']
        obstacles = parking_lot_data.get('obstacles', [])
        
        # Create configuration components
        car = CarDimensions(**config_data.get('car', {}))
        grid = GridConfig(**config_data.get('grid', {}))
        
        road_data = config_data.get('road_modules', {})
        road_modules = RoadModuleSpecs(
            single_lane_min_width=road_data.get('single_lane', {}).get('min_width', 2.5),
            single_lane_max_width=road_data.get('single_lane', {}).get('max_width', 3.5),
            double_lane_min_width=road_data.get('double_lane', {}).get('min_width', 5.0),
            double_lane_max_width=road_data.get('double_lane', {}).get('max_width', 6.0),
            turning_area_min_size=road_data.get('turning_area', {}).get('min_size', 5.0)
        )
        
        module_generation = ModuleGenerationParams(**config_data.get('module_generation', {}))
        qubo_weights = QUBOWeights(**config_data.get('qubo_weights', {}))
        quantum_annealing = QuantumAnnealingParams(**config_data.get('quantum_annealing', {}))
        solver = SolverConfig(**config_data.get('solver', {}))
        visualization = VisualizationConfig(**config_data.get('visualization', {}))
        validation = ValidationParams(**config_data.get('validation', {}))
        
        return cls(
            width=width,
            height=height,
            obstacles=obstacles,
            car=car,
            grid=grid,
            road_modules=road_modules,
            module_generation=module_generation,
            qubo_weights=qubo_weights,
            quantum_annealing=quantum_annealing,
            solver=solver,
            visualization=visualization,
            validation=validation
        )
    
    def to_yaml(self, output_path: str) -> None:
        """
        Save configuration to a YAML file.
        
        Args:
            output_path: Path where to save the configuration
        """
        config_dict = {
            'parking_lot': {
                'width': self.width,
                'height': self.height,
                'obstacles': self.obstacles
            },
            'car': {
                'width': self.car.width,
                'length': self.car.length,
                'clearance': self.car.clearance
            },
            'grid': {
                'cell_size': self.grid.cell_size
            },
            'road_modules': {
                'single_lane': {
                    'min_width': self.road_modules.single_lane_min_width,
                    'max_width': self.road_modules.single_lane_max_width
                },
                'double_lane': {
                    'min_width': self.road_modules.double_lane_min_width,
                    'max_width': self.road_modules.double_lane_max_width
                },
                'turning_area': {
                    'min_size': self.road_modules.turning_area_min_size
                }
            },
            'module_generation': {
                'min_module_length': self.module_generation.min_module_length,
                'max_module_length': self.module_generation.max_module_length,
                'step_size': self.module_generation.step_size
            },
            'qubo_weights': {
                'overlap_penalty': self.qubo_weights.overlap_penalty,
                'coverage_reward': self.qubo_weights.coverage_reward,
                'connectivity_reward': self.qubo_weights.connectivity_reward,
                'area_penalty': self.qubo_weights.area_penalty
            },
            'quantum_annealing': {
                'num_reads': self.quantum_annealing.num_reads,
                'annealing_time': self.quantum_annealing.annealing_time,
                'chain_strength': self.quantum_annealing.chain_strength,
                'auto_scale': self.quantum_annealing.auto_scale
            },
            'solver': {
                'use_simulator': self.solver.use_simulator,
                'simulator_type': self.solver.simulator_type
            },
            'visualization': {
                'figure_size': list(self.visualization.figure_size),
                'dpi': self.visualization.dpi,
                'colors': self.visualization.colors
            },
            'validation': {
                'check_overlap': self.validation.check_overlap,
                'check_connectivity': self.validation.check_connectivity,
                'min_parking_spaces': self.validation.min_parking_spaces,
                'max_road_ratio': self.validation.max_road_ratio
            }
        }
        
        with open(output_path, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, indent=2)
    
    def get_grid_dimensions(self) -> Tuple[int, int]:
        """
        Get the grid dimensions in cells.
        
        Returns:
            Tuple of (width_cells, height_cells)
        """
        width_cells = int(self.width / self.grid.cell_size)
        height_cells = int(self.height / self.grid.cell_size)
        return width_cells, height_cells
    
    def validate(self) -> List[str]:
        """
        Validate the configuration parameters.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        if self.width <= 0:
            errors.append("Parking lot width must be positive")
        if self.height <= 0:
            errors.append("Parking lot height must be positive")
        if self.grid.cell_size <= 0:
            errors.append("Grid cell size must be positive")
        if self.car.width <= 0:
            errors.append("Car width must be positive")
        if self.car.length <= 0:
            errors.append("Car length must be positive")
            
        # Check obstacle validity
        for i, (x, y, w, h) in enumerate(self.obstacles):
            if x < 0 or y < 0:
                errors.append(f"Obstacle {i}: position must be non-negative")
            if w <= 0 or h <= 0:
                errors.append(f"Obstacle {i}: dimensions must be positive")
            if x + w > self.width or y + h > self.height:
                errors.append(f"Obstacle {i}: extends beyond parking lot boundaries")
                
        return errors
