"""
Layout visualization for parking lot optimization.

This module provides tools for visualizing parking lot layouts,
road modules, and optimization results.
"""

from typing import List, Dict, Optional, Tuple, Any
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
import seaborn as sns
from matplotlib.colors import ListedColormap

from ..core.config import ParkingLotConfig
from ..core.geometry import Rectangle
from ..preprocessing.module_generator import RoadModule


class LayoutPlotter:
    """
    Visualizes parking lot layouts and optimization results.
    
    This class provides comprehensive visualization capabilities for
    displaying parking lots, road modules, obstacles, and solutions.
    """
    
    def __init__(self, config: ParkingLotConfig):
        """
        Initialize the layout plotter.
        
        Args:
            config: Parking lot configuration
        """
        self.config = config
        self.colors = config.visualization.colors
        self.figure_size = config.visualization.figure_size
        self.dpi = config.visualization.dpi
        
        # Set up matplotlib style
        plt.style.use('default')
        sns.set_palette("husl")
    
    def plot_parking_lot_base(self, ax: Optional[plt.Axes] = None, 
                            show_grid: bool = True) -> plt.Axes:
        """
        Plot the base parking lot with obstacles and grid.
        
        Args:
            ax: Matplotlib axes (creates new if None)
            show_grid: Whether to show grid lines
            
        Returns:
            Matplotlib axes object
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
        
        # Set up the plot area
        ax.set_xlim(0, self.config.width)
        ax.set_ylim(0, self.config.height)
        ax.set_aspect('equal')
        
        # Fill parking lot background
        parking_lot_rect = patches.Rectangle(
            (0, 0), self.config.width, self.config.height,
            facecolor=self.colors['parking_space'],
            edgecolor='black',
            linewidth=2
        )
        ax.add_patch(parking_lot_rect)
        
        # Draw obstacles
        for x, y, width, height in self.config.obstacles:
            obstacle_rect = patches.Rectangle(
                (x, y), width, height,
                facecolor=self.colors['obstacle'],
                edgecolor='darkred',
                linewidth=1.5,
                alpha=0.8
            )
            ax.add_patch(obstacle_rect)
        
        # Draw grid if requested
        if show_grid:
            self._draw_grid(ax)
        
        # Labels and title
        ax.set_xlabel('X (meters)', fontsize=12)
        ax.set_ylabel('Y (meters)', fontsize=12)
        ax.set_title('Parking Lot Layout', fontsize=14, fontweight='bold')
        
        return ax
    
    def _draw_grid(self, ax: plt.Axes) -> None:
        """Draw grid lines on the plot."""
        cell_size = self.config.grid.cell_size
        
        # Vertical grid lines
        for x in np.arange(0, self.config.width + cell_size, cell_size):
            ax.axvline(x, color=self.colors['grid_lines'], 
                      linewidth=0.5, alpha=0.3)
        
        # Horizontal grid lines
        for y in np.arange(0, self.config.height + cell_size, cell_size):
            ax.axhline(y, color=self.colors['grid_lines'], 
                      linewidth=0.5, alpha=0.3)
    
    def plot_candidate_modules(self, modules: List[RoadModule], 
                             max_modules: int = 50,
                             ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        Plot candidate road modules.
        
        Args:
            modules: List of candidate modules
            max_modules: Maximum number of modules to display
            ax: Matplotlib axes (creates new if None)
            
        Returns:
            Matplotlib axes object
        """
        if ax is None:
            ax = self.plot_parking_lot_base()
        
        # Limit number of modules for clarity
        display_modules = modules[:max_modules] if len(modules) > max_modules else modules
        
        # Color map for different module types
        type_colors = {
            'single_lane': 'lightblue',
            'double_lane': 'lightgreen',
            'turning_area': 'lightyellow'
        }
        
        for i, module in enumerate(display_modules):
            color = type_colors.get(module.module_type, 'lightgray')
            
            module_rect = patches.Rectangle(
                (module.rectangle.x, module.rectangle.y),
                module.rectangle.width, module.rectangle.height,
                facecolor=color,
                edgecolor='blue',
                linewidth=1,
                alpha=0.6
            )
            ax.add_patch(module_rect)
            
            # Add module ID label
            center = module.rectangle.center
            ax.text(center.x, center.y, str(module.id), 
                   ha='center', va='center', fontsize=8, fontweight='bold')
        
        ax.set_title(f'Candidate Road Modules (showing {len(display_modules)} of {len(modules)})',
                    fontsize=14, fontweight='bold')
        
        # Add legend
        legend_elements = [
            patches.Patch(color=color, label=module_type.replace('_', ' ').title())
            for module_type, color in type_colors.items()
        ]
        ax.legend(handles=legend_elements, loc='upper right')
        
        return ax
    
    def plot_solution(self, selected_modules: List[RoadModule],
                     all_modules: Optional[List[RoadModule]] = None,
                     ax: Optional[plt.Axes] = None,
                     show_accessibility: bool = True) -> plt.Axes:
        """
        Plot the solution with selected road modules.
        
        Args:
            selected_modules: List of selected modules
            all_modules: List of all candidate modules (for context)
            ax: Matplotlib axes (creates new if None)
            show_accessibility: Whether to show parking accessibility
            
        Returns:
            Matplotlib axes object
        """
        if ax is None:
            ax = self.plot_parking_lot_base()
        
        # Plot unselected modules in light gray (if provided)
        if all_modules:
            selected_ids = {module.id for module in selected_modules}
            unselected_modules = [m for m in all_modules if m.id not in selected_ids]
            
            for module in unselected_modules:
                module_rect = patches.Rectangle(
                    (module.rectangle.x, module.rectangle.y),
                    module.rectangle.width, module.rectangle.height,
                    facecolor='lightgray',
                    edgecolor='gray',
                    linewidth=0.5,
                    alpha=0.3
                )
                ax.add_patch(module_rect)
        
        # Plot selected modules
        for module in selected_modules:
            # Color based on module type
            if module.module_type == 'single_lane':
                color = self.colors['road']
            elif module.module_type == 'double_lane':
                color = 'darkgray'
            else:  # turning_area
                color = self.colors['selected_module']
            
            module_rect = patches.Rectangle(
                (module.rectangle.x, module.rectangle.y),
                module.rectangle.width, module.rectangle.height,
                facecolor=color,
                edgecolor='black',
                linewidth=2,
                alpha=0.8
            )
            ax.add_patch(module_rect)
            
            # Add accessibility value if requested
            if show_accessibility:
                center = module.rectangle.center
                ax.text(center.x, center.y, f'{module.parking_accessibility_value:.0f}',
                       ha='center', va='center', fontsize=10, fontweight='bold',
                       color='white', bbox=dict(boxstyle='round,pad=0.3', 
                                              facecolor='black', alpha=0.7))
        
        ax.set_title(f'Optimized Road Layout ({len(selected_modules)} modules selected)',
                    fontsize=14, fontweight='bold')
        
        return ax
    
    def plot_solution_comparison(self, solutions: List[Dict[str, Any]],
                               titles: Optional[List[str]] = None) -> plt.Figure:
        """
        Plot multiple solutions for comparison.
        
        Args:
            solutions: List of solution dictionaries
            titles: Optional titles for each solution
            
        Returns:
            Matplotlib figure object
        """
        n_solutions = len(solutions)
        if n_solutions == 0:
            raise ValueError("No solutions provided")
        
        # Determine subplot layout
        cols = min(3, n_solutions)
        rows = (n_solutions + cols - 1) // cols
        
        fig, axes = plt.subplots(rows, cols, figsize=(cols * 6, rows * 6), dpi=self.dpi)
        if n_solutions == 1:
            axes = [axes]
        elif rows == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        for i, solution in enumerate(solutions):
            ax = axes[i]
            selected_modules = solution.get('selected_modules', [])
            
            # Plot solution
            self.plot_solution(selected_modules, ax=ax, show_accessibility=False)
            
            # Add title
            if titles and i < len(titles):
                title = titles[i]
            else:
                metrics = solution.get('performance_metrics', {})
                title = f'Solution {i+1}\n{len(selected_modules)} modules, ' \
                       f'{metrics.get("total_parking_spaces", 0):.0f} spaces'
            
            ax.set_title(title, fontsize=12, fontweight='bold')
        
        # Hide unused subplots
        for i in range(n_solutions, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        return fig
    
    def plot_module_heatmap(self, modules: List[RoadModule], 
                          value_key: str = 'parking_accessibility_value',
                          ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        Plot a heatmap of module values.
        
        Args:
            modules: List of modules to visualize
            value_key: Attribute to use for heatmap values
            ax: Matplotlib axes (creates new if None)
            
        Returns:
            Matplotlib axes object
        """
        if ax is None:
            ax = self.plot_parking_lot_base(show_grid=False)
        
        # Extract values
        values = []
        rectangles = []
        
        for module in modules:
            if hasattr(module, value_key):
                values.append(getattr(module, value_key))
                rectangles.append(module.rectangle)
        
        if not values:
            ax.set_title(f'No modules with {value_key}', fontsize=14)
            return ax
        
        # Normalize values for color mapping
        values = np.array(values)
        norm_values = (values - values.min()) / (values.max() - values.min()) if values.max() > values.min() else np.zeros_like(values)
        
        # Create colormap
        cmap = plt.cm.viridis
        
        # Plot modules with color based on value
        for i, (rect, norm_val) in enumerate(zip(rectangles, norm_values)):
            color = cmap(norm_val)
            
            module_rect = patches.Rectangle(
                (rect.x, rect.y), rect.width, rect.height,
                facecolor=color,
                edgecolor='black',
                linewidth=0.5,
                alpha=0.8
            )
            ax.add_patch(module_rect)
        
        # Add colorbar
        sm = plt.cm.ScalarMappable(cmap=cmap, norm=plt.Normalize(vmin=values.min(), vmax=values.max()))
        sm.set_array([])
        cbar = plt.colorbar(sm, ax=ax)
        cbar.set_label(value_key.replace('_', ' ').title(), fontsize=12)
        
        ax.set_title(f'Module Heatmap: {value_key.replace("_", " ").title()}',
                    fontsize=14, fontweight='bold')
        
        return ax
    
    def plot_connectivity_graph(self, modules: List[RoadModule],
                              adjacency_matrix: np.ndarray,
                              ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        Plot module connectivity as a graph overlay.
        
        Args:
            modules: List of modules
            adjacency_matrix: Boolean adjacency matrix
            ax: Matplotlib axes (creates new if None)
            
        Returns:
            Matplotlib axes object
        """
        if ax is None:
            ax = self.plot_parking_lot_base()
        
        # Plot modules
        for module in modules:
            module_rect = patches.Rectangle(
                (module.rectangle.x, module.rectangle.y),
                module.rectangle.width, module.rectangle.height,
                facecolor=self.colors['selected_module'],
                edgecolor='black',
                linewidth=1,
                alpha=0.7
            )
            ax.add_patch(module_rect)
        
        # Plot connections
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i < j and adjacency_matrix[i, j]:
                    center1 = module1.rectangle.center
                    center2 = module2.rectangle.center
                    
                    ax.plot([center1.x, center2.x], [center1.y, center2.y],
                           'r-', linewidth=2, alpha=0.6)
        
        ax.set_title('Module Connectivity Graph', fontsize=14, fontweight='bold')
        return ax
    
    def save_plot(self, fig: plt.Figure, filename: str, 
                 format: str = 'png', bbox_inches: str = 'tight') -> None:
        """
        Save a plot to file.
        
        Args:
            fig: Matplotlib figure to save
            filename: Output filename
            format: File format ('png', 'pdf', 'svg', etc.)
            bbox_inches: Bounding box setting
        """
        fig.savefig(filename, format=format, dpi=self.dpi, 
                   bbox_inches=bbox_inches, facecolor='white')
        print(f"Plot saved to {filename}")
    
    def create_animation_frames(self, solution_sequence: List[List[RoadModule]],
                              frame_duration: float = 0.5) -> List[plt.Figure]:
        """
        Create animation frames showing solution evolution.
        
        Args:
            solution_sequence: List of solutions (each is a list of modules)
            frame_duration: Duration for each frame
            
        Returns:
            List of matplotlib figures
        """
        frames = []
        
        for i, selected_modules in enumerate(solution_sequence):
            fig, ax = plt.subplots(figsize=self.figure_size, dpi=self.dpi)
            self.plot_solution(selected_modules, ax=ax)
            ax.set_title(f'Solution Evolution - Step {i+1}', 
                        fontsize=14, fontweight='bold')
            frames.append(fig)
        
        return frames
