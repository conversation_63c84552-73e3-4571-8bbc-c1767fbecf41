"""
Performance metrics analysis and visualization.

This module provides tools for analyzing and visualizing performance
metrics from parking lot optimization results.
"""

from typing import List, Dict, Any, Optional, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from matplotlib.patches import Patch

from ..core.config import ParkingLotConfig


class MetricsAnalyzer:
    """
    Analyzes and visualizes performance metrics from optimization results.
    
    This class provides comprehensive analysis tools for understanding
    solution quality, parameter sensitivity, and optimization performance.
    """
    
    def __init__(self, config: ParkingLotConfig):
        """
        Initialize the metrics analyzer.
        
        Args:
            config: Parking lot configuration
        """
        self.config = config
        self.figure_size = config.visualization.figure_size
        self.dpi = config.visualization.dpi
        
        # Set up plotting style
        sns.set_style("whitegrid")
        plt.rcParams['figure.figsize'] = self.figure_size
        plt.rcParams['figure.dpi'] = self.dpi
    
    def plot_solution_metrics(self, solutions: List[Dict[str, Any]],
                            solution_labels: Optional[List[str]] = None) -> plt.Figure:
        """
        Plot key metrics for multiple solutions.
        
        Args:
            solutions: List of solution dictionaries
            solution_labels: Optional labels for solutions
            
        Returns:
            Matplotlib figure object
        """
        if not solutions:
            raise ValueError("No solutions provided")
        
        # Extract metrics
        metrics_data = []
        for i, solution in enumerate(solutions):
            metrics = solution.get('performance_metrics', {})
            validation = solution.get('validation', {})
            
            label = solution_labels[i] if solution_labels and i < len(solution_labels) else f'Solution {i+1}'
            
            metrics_data.append({
                'Solution': label,
                'Parking Spaces': metrics.get('total_parking_spaces', 0),
                'Road Area (m²)': metrics.get('total_road_area', 0),
                'Area Efficiency': metrics.get('area_efficiency', 0),
                'Connectivity': metrics.get('connectivity_ratio', 0),
                'Space Utilization': metrics.get('space_utilization', 0),
                'Valid': validation.get('is_valid', False)
            })
        
        df = pd.DataFrame(metrics_data)
        
        # Create subplots
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        axes = axes.flatten()
        
        # Plot each metric
        metrics_to_plot = [
            ('Parking Spaces', 'Number of Parking Spaces'),
            ('Road Area (m²)', 'Total Road Area (m²)'),
            ('Area Efficiency', 'Area Efficiency (spaces/m²)'),
            ('Connectivity', 'Connectivity Ratio'),
            ('Space Utilization', 'Space Utilization Ratio'),
        ]
        
        for i, (metric, title) in enumerate(metrics_to_plot):
            ax = axes[i]
            
            # Color bars based on validity
            colors = ['green' if valid else 'red' for valid in df['Valid']]
            
            bars = ax.bar(df['Solution'], df[metric], color=colors, alpha=0.7)
            ax.set_title(title, fontsize=12, fontweight='bold')
            ax.set_ylabel(metric)
            
            # Rotate x-axis labels if needed
            if len(df) > 5:
                ax.tick_params(axis='x', rotation=45)
            
            # Add value labels on bars
            for bar, value in zip(bars, df[metric]):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.2f}', ha='center', va='bottom', fontsize=9)
        
        # Validity summary in the last subplot
        ax = axes[5]
        valid_counts = df['Valid'].value_counts()
        colors = ['green', 'red']
        labels = ['Valid', 'Invalid']
        
        wedges, texts, autotexts = ax.pie(valid_counts.values, labels=labels, 
                                         colors=colors, autopct='%1.1f%%',
                                         startangle=90)
        ax.set_title('Solution Validity', fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        return fig
    
    def plot_parameter_sensitivity(self, parameter_results: Dict[str, List[Dict[str, Any]]],
                                 metric_key: str = 'total_parking_spaces') -> plt.Figure:
        """
        Plot parameter sensitivity analysis.
        
        Args:
            parameter_results: Dictionary mapping parameter values to results
            metric_key: Metric to analyze sensitivity for
            
        Returns:
            Matplotlib figure object
        """
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        param_values = []
        metric_means = []
        metric_stds = []
        
        for param_value, results in parameter_results.items():
            param_values.append(param_value)
            
            # Extract metric values
            metric_values = []
            for result in results:
                metrics = result.get('performance_metrics', {})
                metric_values.append(metrics.get(metric_key, 0))
            
            metric_means.append(np.mean(metric_values))
            metric_stds.append(np.std(metric_values))
        
        # Sort by parameter value
        sorted_data = sorted(zip(param_values, metric_means, metric_stds))
        param_values, metric_means, metric_stds = zip(*sorted_data)
        
        # Plot with error bars
        ax.errorbar(param_values, metric_means, yerr=metric_stds, 
                   marker='o', capsize=5, capthick=2, linewidth=2)
        
        ax.set_xlabel('Parameter Value', fontsize=12)
        ax.set_ylabel(metric_key.replace('_', ' ').title(), fontsize=12)
        ax.set_title(f'Parameter Sensitivity: {metric_key.replace("_", " ").title()}',
                    fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        return fig
    
    def plot_convergence_analysis(self, energy_history: List[float],
                                iteration_history: Optional[List[int]] = None) -> plt.Figure:
        """
        Plot convergence analysis of the optimization.
        
        Args:
            energy_history: List of energy values over iterations
            iteration_history: Optional list of iteration numbers
            
        Returns:
            Matplotlib figure object
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        iterations = iteration_history if iteration_history else range(len(energy_history))
        
        # Energy convergence plot
        ax1.plot(iterations, energy_history, 'b-', linewidth=2, alpha=0.7)
        ax1.set_xlabel('Iteration', fontsize=12)
        ax1.set_ylabel('Energy', fontsize=12)
        ax1.set_title('Energy Convergence', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)
        
        # Add best energy line
        best_energy = min(energy_history)
        ax1.axhline(y=best_energy, color='red', linestyle='--', 
                   label=f'Best Energy: {best_energy:.2f}')
        ax1.legend()
        
        # Energy distribution histogram
        ax2.hist(energy_history, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(x=best_energy, color='red', linestyle='--', linewidth=2,
                   label=f'Best Energy: {best_energy:.2f}')
        ax2.set_xlabel('Energy', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.set_title('Energy Distribution', fontsize=14, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def plot_qubo_analysis(self, qubo_analysis: Dict[str, Any]) -> plt.Figure:
        """
        Plot QUBO matrix analysis.
        
        Args:
            qubo_analysis: QUBO analysis results
            
        Returns:
            Matplotlib figure object
        """
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Matrix structure
        ax = axes[0, 0]
        structure_data = [
            qubo_analysis['num_linear_terms'],
            qubo_analysis['num_quadratic_terms']
        ]
        labels = ['Linear Terms', 'Quadratic Terms']
        colors = ['lightblue', 'lightcoral']
        
        bars = ax.bar(labels, structure_data, color=colors)
        ax.set_title('QUBO Matrix Structure', fontsize=12, fontweight='bold')
        ax.set_ylabel('Number of Terms')
        
        # Add value labels
        for bar, value in zip(bars, structure_data):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{value}', ha='center', va='bottom')
        
        # Coefficient statistics
        ax = axes[0, 1]
        coeff_stats = qubo_analysis['coefficient_stats']
        stats_data = [coeff_stats['min'], coeff_stats['mean'], coeff_stats['max']]
        stats_labels = ['Min', 'Mean', 'Max']
        
        ax.bar(stats_labels, stats_data, color='lightgreen')
        ax.set_title('Coefficient Statistics', fontsize=12, fontweight='bold')
        ax.set_ylabel('Coefficient Value')
        
        # Weight distribution
        ax = axes[1, 0]
        weights = qubo_analysis['weight_distribution']
        weight_names = list(weights.keys())
        weight_values = list(weights.values())
        
        ax.bar(weight_names, weight_values, color='orange', alpha=0.7)
        ax.set_title('QUBO Weight Distribution', fontsize=12, fontweight='bold')
        ax.set_ylabel('Weight Value')
        ax.tick_params(axis='x', rotation=45)
        
        # Matrix density and properties
        ax = axes[1, 1]
        properties = [
            ('Variables', qubo_analysis['num_variables']),
            ('Density', qubo_analysis['matrix_density']),
            ('Positive Coeffs', qubo_analysis['positive_coefficients']),
            ('Negative Coeffs', qubo_analysis['negative_coefficients'])
        ]
        
        prop_names, prop_values = zip(*properties)
        ax.bar(prop_names, prop_values, color='purple', alpha=0.7)
        ax.set_title('Matrix Properties', fontsize=12, fontweight='bold')
        ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def plot_solution_comparison_radar(self, solutions: List[Dict[str, Any]],
                                     solution_labels: Optional[List[str]] = None) -> plt.Figure:
        """
        Create a radar chart comparing multiple solutions.
        
        Args:
            solutions: List of solution dictionaries
            solution_labels: Optional labels for solutions
            
        Returns:
            Matplotlib figure object
        """
        if not solutions:
            raise ValueError("No solutions provided")
        
        # Define metrics for radar chart
        metrics = [
            'total_parking_spaces',
            'area_efficiency', 
            'connectivity_ratio',
            'space_utilization',
            'coverage_uniformity'
        ]
        
        metric_labels = [
            'Parking Spaces',
            'Area Efficiency',
            'Connectivity',
            'Space Utilization',
            'Coverage Uniformity'
        ]
        
        # Extract and normalize data
        solution_data = []
        for solution in solutions:
            perf_metrics = solution.get('performance_metrics', {})
            data = []
            for metric in metrics:
                value = perf_metrics.get(metric, 0)
                data.append(value)
            solution_data.append(data)
        
        # Normalize each metric to 0-1 scale
        solution_data = np.array(solution_data)
        for i in range(len(metrics)):
            col = solution_data[:, i]
            if col.max() > col.min():
                solution_data[:, i] = (col - col.min()) / (col.max() - col.min())
        
        # Create radar chart
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # Complete the circle
        
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        
        colors = plt.cm.Set1(np.linspace(0, 1, len(solutions)))
        
        for i, (data, color) in enumerate(zip(solution_data, colors)):
            values = data.tolist()
            values += values[:1]  # Complete the circle
            
            label = solution_labels[i] if solution_labels and i < len(solution_labels) else f'Solution {i+1}'
            ax.plot(angles, values, 'o-', linewidth=2, label=label, color=color)
            ax.fill(angles, values, alpha=0.25, color=color)
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metric_labels)
        ax.set_ylim(0, 1)
        ax.set_title('Solution Comparison (Normalized Metrics)', 
                    fontsize=14, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        ax.grid(True)
        
        return fig
    
    def generate_performance_report(self, solution: Dict[str, Any]) -> str:
        """
        Generate a text performance report for a solution.
        
        Args:
            solution: Solution dictionary
            
        Returns:
            Formatted performance report string
        """
        metrics = solution.get('performance_metrics', {})
        validation = solution.get('validation', {})
        summary = solution.get('solution_summary', {})
        
        report = f"""
PARKING LOT OPTIMIZATION PERFORMANCE REPORT
==========================================

SOLUTION OVERVIEW:
- Valid Solution: {'Yes' if validation.get('is_valid', False) else 'No'}
- Selected Modules: {summary.get('num_selected_modules', 0)}
- Constraint Violations: {summary.get('constraint_violations', 0)}

SPACE UTILIZATION:
- Total Parking Spaces: {metrics.get('total_parking_spaces', 0):.0f}
- Total Road Area: {metrics.get('total_road_area', 0):.2f} m²
- Space Utilization: {metrics.get('space_utilization', 0):.1%}
- Parking Density: {metrics.get('parking_density', 0):.3f} spaces/m²

EFFICIENCY METRICS:
- Area Efficiency: {metrics.get('area_efficiency', 0):.3f} spaces/m²
- Connectivity Ratio: {metrics.get('connectivity_ratio', 0):.1%}
- Coverage Uniformity: {metrics.get('coverage_uniformity', 0):.3f}

NETWORK ANALYSIS:
- Connected Components: {validation.get('num_connected_components', 0)}
- Network Connectivity: {'Yes' if summary.get('network_connectivity', False) else 'No'}
- Largest Component: {validation.get('largest_component_size', 0)} modules

CONSTRAINT SATISFACTION:
- No Overlaps: {'Yes' if len(validation.get('overlap_violations', [])) == 0 else 'No'}
- Minimum Spaces Met: {'Yes' if validation.get('min_spaces_satisfied', False) else 'No'}
- Area Constraint Met: {'Yes' if validation.get('area_constraint_satisfied', False) else 'No'}
"""
        
        return report
    
    def export_metrics_csv(self, solutions: List[Dict[str, Any]], 
                          filename: str) -> None:
        """
        Export solution metrics to CSV file.
        
        Args:
            solutions: List of solution dictionaries
            filename: Output CSV filename
        """
        metrics_data = []
        
        for i, solution in enumerate(solutions):
            metrics = solution.get('performance_metrics', {})
            validation = solution.get('validation', {})
            
            row = {
                'solution_id': i,
                'is_valid': validation.get('is_valid', False),
                'num_modules': len(solution.get('selected_modules', [])),
                'total_parking_spaces': metrics.get('total_parking_spaces', 0),
                'total_road_area': metrics.get('total_road_area', 0),
                'area_efficiency': metrics.get('area_efficiency', 0),
                'connectivity_ratio': metrics.get('connectivity_ratio', 0),
                'space_utilization': metrics.get('space_utilization', 0),
                'constraint_violations': len(validation.get('overlap_violations', [])),
                'connected_components': validation.get('num_connected_components', 0)
            }
            
            metrics_data.append(row)
        
        df = pd.DataFrame(metrics_data)
        df.to_csv(filename, index=False)
        print(f"Metrics exported to {filename}")
