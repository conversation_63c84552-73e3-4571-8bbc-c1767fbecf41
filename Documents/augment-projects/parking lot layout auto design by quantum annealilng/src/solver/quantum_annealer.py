"""
Quantum annealing solver interface for parking lot optimization.

This module implements Phase 3 of the algorithm: quantum annealing integration
with D-Wave quantum computers or simulators.
"""

from typing import Dict, List, Tuple, Optional, Any
import numpy as np
import dimod
import neal

# Optional D-Wave imports
try:
    from dwave.system import DWaveSampler, EmbeddingComposite
    from dwave.samplers import SimulatedAnnealingSampler
    DWAVE_AVAILABLE = True
except ImportError:
    DWAVE_AVAILABLE = False
    DWaveSampler = None
    EmbeddingComposite = None
    SimulatedAnnealingSampler = None

from ..core.config import ParkingLotConfig
from ..qubo.formulation import QUBOFormulator
from .postprocessor import SolutionPostProcessor


class QuantumAnnealer:
    """
    Interface for quantum annealing solvers.
    
    This class provides a unified interface for solving QUBO problems
    using either D-Wave quantum hardware or classical simulators.
    """
    
    def __init__(self, config: Optional[ParkingLotConfig] = None):
        """
        Initialize the quantum annealer.
        
        Args:
            config: Parking lot configuration (optional)
        """
        self.config = config
        self.sampler = None
        self.last_solution = None
        self.last_sample_set = None
        
    def _get_sampler(self, use_simulator: bool = True,
                    simulator_type: str = "neal") -> dimod.Sampler:
        """
        Get the appropriate sampler based on configuration.

        Args:
            use_simulator: Whether to use simulator instead of quantum hardware
            simulator_type: Type of simulator ("neal", "simulated_annealing")

        Returns:
            Configured sampler object
        """
        if use_simulator:
            if simulator_type == "neal":
                return neal.SimulatedAnnealingSampler()
            elif simulator_type == "simulated_annealing" and DWAVE_AVAILABLE:
                return SimulatedAnnealingSampler()
            else:
                # Fall back to neal if D-Wave not available
                return neal.SimulatedAnnealingSampler()
        else:
            # Use D-Wave quantum hardware
            if not DWAVE_AVAILABLE:
                print("D-Wave system not available, falling back to simulator...")
                return neal.SimulatedAnnealingSampler()

            try:
                return EmbeddingComposite(DWaveSampler())
            except Exception as e:
                print(f"Failed to connect to D-Wave hardware: {e}")
                print("Falling back to simulator...")
                return neal.SimulatedAnnealingSampler()
    
    def solve(self, qubo_matrix: Dict[Tuple[int, int], float],
              num_reads: Optional[int] = None,
              use_simulator: Optional[bool] = None,
              **kwargs) -> Dict[str, Any]:
        """
        Solve the QUBO problem using quantum annealing.
        
        Args:
            qubo_matrix: QUBO matrix in dimod format
            num_reads: Number of annealing runs
            use_simulator: Whether to use simulator (overrides config)
            **kwargs: Additional solver parameters
            
        Returns:
            Dictionary with solution results
        """
        # Use config values if not specified
        if self.config:
            num_reads = num_reads or self.config.quantum_annealing.num_reads
            use_simulator = use_simulator if use_simulator is not None else self.config.solver.use_simulator
            simulator_type = self.config.solver.simulator_type
        else:
            num_reads = num_reads or 1000
            use_simulator = use_simulator if use_simulator is not None else True
            simulator_type = "neal"
        
        # Get sampler
        sampler = self._get_sampler(use_simulator, simulator_type)
        
        # Convert QUBO to BinaryQuadraticModel
        bqm = dimod.BinaryQuadraticModel.from_qubo(qubo_matrix)
        
        # Prepare solver parameters
        solver_params = {
            'num_reads': num_reads
        }
        
        # Add configuration-specific parameters
        if self.config and not use_simulator:
            solver_params.update({
                'annealing_time': self.config.quantum_annealing.annealing_time,
                'chain_strength': self.config.quantum_annealing.chain_strength,
                'auto_scale': self.config.quantum_annealing.auto_scale
            })
        
        # Add any additional parameters
        solver_params.update(kwargs)
        
        # Solve the problem
        try:
            sample_set = sampler.sample(bqm, **solver_params)
            self.last_sample_set = sample_set
            
            # Get the best solution
            best_sample = sample_set.first
            solution = dict(best_sample.sample)
            energy = best_sample.energy
            
            # Store solution
            self.last_solution = {
                'solution': solution,
                'energy': energy,
                'sample_set': sample_set,
                'solver_info': {
                    'sampler_type': type(sampler).__name__,
                    'num_reads': num_reads,
                    'use_simulator': use_simulator
                }
            }
            
            return self.last_solution
            
        except Exception as e:
            raise RuntimeError(f"Quantum annealing failed: {e}")
    
    def solve_with_formulator(self, formulator: QUBOFormulator,
                            **kwargs) -> Dict[str, Any]:
        """
        Solve using a QUBO formulator object.
        
        Args:
            formulator: QUBOFormulator instance
            **kwargs: Additional solver parameters
            
        Returns:
            Dictionary with solution results and interpretation
        """
        # Build QUBO matrix
        qubo_matrix = formulator.build_qubo_matrix()
        
        # Solve the problem
        solution_result = self.solve(qubo_matrix, **kwargs)
        
        # Interpret the solution
        interpretation = formulator.get_solution_interpretation(
            solution_result['solution']
        )
        
        # Combine results
        solution_result['interpretation'] = interpretation
        
        return solution_result
    
    def analyze_solution_quality(self, sample_set: Optional[dimod.SampleSet] = None) -> Dict[str, Any]:
        """
        Analyze the quality of the solution(s).
        
        Args:
            sample_set: SampleSet to analyze (uses last if not provided)
            
        Returns:
            Dictionary with solution quality metrics
        """
        if sample_set is None:
            sample_set = self.last_sample_set
            
        if sample_set is None:
            raise ValueError("No sample set available for analysis")
        
        # Extract energies and occurrences
        energies = sample_set.data_vectors['energy']
        num_occurrences = sample_set.data_vectors['num_occurrences']
        
        # Calculate statistics
        best_energy = min(energies)
        worst_energy = max(energies)
        mean_energy = np.average(energies, weights=num_occurrences)
        
        # Find unique solutions
        unique_solutions = len(set(tuple(sorted(sample.items())) 
                                 for sample in sample_set.samples()))
        
        # Calculate success rate (solutions within 1% of best)
        energy_threshold = best_energy + 0.01 * abs(best_energy)
        successful_reads = sum(occ for energy, occ in zip(energies, num_occurrences)
                             if energy <= energy_threshold)
        success_rate = successful_reads / sum(num_occurrences)
        
        # Calculate energy gap
        sorted_energies = sorted(set(energies))
        energy_gap = sorted_energies[1] - sorted_energies[0] if len(sorted_energies) > 1 else 0
        
        return {
            'best_energy': best_energy,
            'worst_energy': worst_energy,
            'mean_energy': mean_energy,
            'energy_std': np.std(energies),
            'energy_gap': energy_gap,
            'unique_solutions': unique_solutions,
            'total_reads': sum(num_occurrences),
            'success_rate': success_rate,
            'energy_distribution': {
                'min': best_energy,
                'max': worst_energy,
                'median': np.median(energies),
                'q25': np.percentile(energies, 25),
                'q75': np.percentile(energies, 75)
            }
        }
    
    def get_multiple_solutions(self, num_solutions: int = 5,
                             sample_set: Optional[dimod.SampleSet] = None) -> List[Dict[str, Any]]:
        """
        Get multiple diverse solutions from the sample set.
        
        Args:
            num_solutions: Number of solutions to return
            sample_set: SampleSet to extract from (uses last if not provided)
            
        Returns:
            List of solution dictionaries
        """
        if sample_set is None:
            sample_set = self.last_sample_set
            
        if sample_set is None:
            raise ValueError("No sample set available")
        
        solutions = []
        seen_solutions = set()
        
        for sample in sample_set.samples():
            # Create a hashable representation of the solution
            solution_tuple = tuple(sorted(sample.items()))
            
            if solution_tuple not in seen_solutions:
                seen_solutions.add(solution_tuple)
                solutions.append({
                    'solution': dict(sample),
                    'energy': sample_set.data_vectors['energy'][len(solutions)],
                    'num_occurrences': sample_set.data_vectors['num_occurrences'][len(solutions)]
                })
                
                if len(solutions) >= num_solutions:
                    break
        
        return solutions
    
    def optimize_parameters(self, qubo_matrix: Dict[Tuple[int, int], float],
                          parameter_ranges: Dict[str, List[float]],
                          num_trials: int = 10) -> Dict[str, Any]:
        """
        Optimize solver parameters using grid search.
        
        Args:
            qubo_matrix: QUBO matrix to solve
            parameter_ranges: Dictionary of parameter names to value lists
            num_trials: Number of trials per parameter combination
            
        Returns:
            Dictionary with optimization results
        """
        best_params = None
        best_energy = float('inf')
        results = []
        
        # Generate parameter combinations
        import itertools
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())
        
        for param_combination in itertools.product(*param_values):
            params = dict(zip(param_names, param_combination))
            
            # Run multiple trials with these parameters
            trial_energies = []
            for _ in range(num_trials):
                result = self.solve(qubo_matrix, **params)
                trial_energies.append(result['energy'])
            
            # Calculate statistics for this parameter combination
            mean_energy = np.mean(trial_energies)
            std_energy = np.std(trial_energies)
            min_energy = min(trial_energies)
            
            results.append({
                'parameters': params.copy(),
                'mean_energy': mean_energy,
                'std_energy': std_energy,
                'min_energy': min_energy,
                'trial_energies': trial_energies
            })
            
            # Update best parameters
            if mean_energy < best_energy:
                best_energy = mean_energy
                best_params = params.copy()
        
        return {
            'best_parameters': best_params,
            'best_energy': best_energy,
            'all_results': results,
            'num_combinations_tested': len(results)
        }
    
    def get_solver_info(self) -> Dict[str, Any]:
        """
        Get information about the current solver configuration.
        
        Returns:
            Dictionary with solver information
        """
        info = {
            'has_config': self.config is not None,
            'last_solution_available': self.last_solution is not None
        }
        
        if self.config:
            info.update({
                'configured_solver': {
                    'use_simulator': self.config.solver.use_simulator,
                    'simulator_type': self.config.solver.simulator_type
                },
                'quantum_annealing_params': {
                    'num_reads': self.config.quantum_annealing.num_reads,
                    'annealing_time': self.config.quantum_annealing.annealing_time,
                    'chain_strength': self.config.quantum_annealing.chain_strength,
                    'auto_scale': self.config.quantum_annealing.auto_scale
                }
            })
        
        if self.last_solution:
            info['last_solver_info'] = self.last_solution['solver_info']
        
        return info
