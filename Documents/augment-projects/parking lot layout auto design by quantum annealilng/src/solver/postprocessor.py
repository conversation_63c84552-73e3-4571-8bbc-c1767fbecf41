"""
Solution post-processing for parking lot optimization.

This module provides utilities for processing and analyzing solutions
from the quantum annealing solver.
"""

from typing import List, Dict, Set, Tuple, Any, Optional
import numpy as np
from ..core.config import ParkingLotConfig
from ..core.geometry import Rectangle, Point
from ..preprocessing.module_generator import RoadModule
from ..qubo.constraints import ConstraintManager


class SolutionPostProcessor:
    """
    Post-processes solutions from quantum annealing to extract
    meaningful parking lot layouts and performance metrics.
    """
    
    def __init__(self, modules: List[RoadModule], config: ParkingLotConfig):
        """
        Initialize the solution post-processor.
        
        Args:
            modules: List of candidate road modules
            config: Parking lot configuration
        """
        self.modules = modules
        self.config = config
        self.constraint_manager = ConstraintManager(modules, config)
        
        # Create module lookup
        self.module_lookup = {module.id: module for module in modules}
    
    def process_solution(self, solution: Dict[int, int]) -> Dict[str, Any]:
        """
        Process a solution from the quantum annealer.
        
        Args:
            solution: Dictionary mapping variable indices to binary values
            
        Returns:
            Dictionary with processed solution information
        """
        # Extract selected modules
        selected_modules = self._extract_selected_modules(solution)
        
        # Validate solution
        validation_result = self.constraint_manager.validate_solution(
            {module.id for module in selected_modules}
        )
        
        # Calculate performance metrics
        performance_metrics = self._calculate_performance_metrics(selected_modules)
        
        # Analyze road network
        network_analysis = self._analyze_road_network(selected_modules)
        
        # Generate layout representation
        layout_representation = self._generate_layout_representation(selected_modules)
        
        return {
            'selected_modules': selected_modules,
            'validation': validation_result,
            'performance_metrics': performance_metrics,
            'network_analysis': network_analysis,
            'layout_representation': layout_representation,
            'solution_summary': self._generate_solution_summary(
                selected_modules, validation_result, performance_metrics
            )
        }
    
    def _extract_selected_modules(self, solution: Dict[int, int]) -> List[RoadModule]:
        """
        Extract selected modules from the solution.
        
        Args:
            solution: Binary solution from quantum annealer
            
        Returns:
            List of selected RoadModule objects
        """
        selected_modules = []
        
        for var_index, value in solution.items():
            if value == 1 and var_index < len(self.modules):
                selected_modules.append(self.modules[var_index])
        
        return selected_modules
    
    def _calculate_performance_metrics(self, selected_modules: List[RoadModule]) -> Dict[str, float]:
        """
        Calculate performance metrics for the solution.
        
        Args:
            selected_modules: List of selected modules
            
        Returns:
            Dictionary with performance metrics
        """
        if not selected_modules:
            return {
                'total_road_area': 0.0,
                'total_parking_spaces': 0.0,
                'area_efficiency': 0.0,
                'space_utilization': 0.0,
                'connectivity_ratio': 0.0
            }
        
        # Calculate basic metrics
        total_road_area = sum(module.area for module in selected_modules)
        total_parking_spaces = sum(module.parking_accessibility_value for module in selected_modules)
        
        # Calculate efficiency metrics
        parking_lot_area = self.config.width * self.config.height
        area_efficiency = total_parking_spaces / total_road_area if total_road_area > 0 else 0
        space_utilization = total_road_area / parking_lot_area
        
        # Calculate connectivity
        connectivity_ratio = self._calculate_connectivity_ratio(selected_modules)
        
        # Calculate coverage metrics
        coverage_metrics = self._calculate_coverage_metrics(selected_modules)
        
        metrics = {
            'total_road_area': total_road_area,
            'total_parking_spaces': total_parking_spaces,
            'area_efficiency': area_efficiency,
            'space_utilization': space_utilization,
            'connectivity_ratio': connectivity_ratio,
            'parking_density': total_parking_spaces / parking_lot_area,
            'road_area_ratio': total_road_area / parking_lot_area
        }
        
        metrics.update(coverage_metrics)
        return metrics
    
    def _calculate_connectivity_ratio(self, selected_modules: List[RoadModule]) -> float:
        """
        Calculate the connectivity ratio of selected modules.
        
        Args:
            selected_modules: List of selected modules
            
        Returns:
            Connectivity ratio (0 to 1)
        """
        if len(selected_modules) <= 1:
            return 1.0 if len(selected_modules) == 1 else 0.0
        
        # Count adjacent pairs
        adjacent_pairs = 0
        total_pairs = len(selected_modules) * (len(selected_modules) - 1) // 2
        
        for i, module1 in enumerate(selected_modules):
            for j, module2 in enumerate(selected_modules):
                if i < j and self.constraint_manager.check_modules_adjacent(module1, module2):
                    adjacent_pairs += 1
        
        return adjacent_pairs / total_pairs if total_pairs > 0 else 0.0
    
    def _calculate_coverage_metrics(self, selected_modules: List[RoadModule]) -> Dict[str, float]:
        """
        Calculate coverage metrics for the parking lot.
        
        Args:
            selected_modules: List of selected modules
            
        Returns:
            Dictionary with coverage metrics
        """
        if not selected_modules:
            return {
                'coverage_uniformity': 0.0,
                'edge_coverage': 0.0,
                'center_coverage': 0.0
            }
        
        # Calculate spatial distribution
        module_centers = [module.center for module in selected_modules]
        
        # Coverage uniformity (how evenly distributed the modules are)
        coverage_uniformity = self._calculate_spatial_uniformity(module_centers)
        
        # Edge vs center coverage
        edge_coverage, center_coverage = self._calculate_edge_center_coverage(module_centers)
        
        return {
            'coverage_uniformity': coverage_uniformity,
            'edge_coverage': edge_coverage,
            'center_coverage': center_coverage
        }
    
    def _calculate_spatial_uniformity(self, points: List[Point]) -> float:
        """
        Calculate how uniformly distributed the points are.
        
        Args:
            points: List of points to analyze
            
        Returns:
            Uniformity score (higher is more uniform)
        """
        if len(points) <= 1:
            return 1.0
        
        # Calculate pairwise distances
        distances = []
        for i, p1 in enumerate(points):
            for j, p2 in enumerate(points):
                if i < j:
                    distances.append(p1.distance_to(p2))
        
        if not distances:
            return 1.0
        
        # Uniformity is inversely related to distance variance
        mean_distance = np.mean(distances)
        distance_variance = np.var(distances)
        
        # Normalize by mean to get coefficient of variation
        cv = np.sqrt(distance_variance) / mean_distance if mean_distance > 0 else 0
        
        # Convert to uniformity score (0 to 1, higher is better)
        uniformity = 1.0 / (1.0 + cv)
        
        return uniformity
    
    def _calculate_edge_center_coverage(self, points: List[Point]) -> Tuple[float, float]:
        """
        Calculate coverage near edges vs center of parking lot.
        
        Args:
            points: List of points to analyze
            
        Returns:
            Tuple of (edge_coverage, center_coverage)
        """
        if not points:
            return 0.0, 0.0
        
        # Define edge and center regions
        edge_threshold = min(self.config.width, self.config.height) * 0.2
        center_x = self.config.width / 2
        center_y = self.config.height / 2
        
        edge_points = 0
        center_points = 0
        
        for point in points:
            # Distance to nearest edge
            edge_distance = min(
                point.x,
                self.config.width - point.x,
                point.y,
                self.config.height - point.y
            )
            
            # Distance to center
            center_distance = Point(center_x, center_y).distance_to(point)
            
            if edge_distance <= edge_threshold:
                edge_points += 1
            
            if center_distance <= edge_threshold:
                center_points += 1
        
        total_points = len(points)
        edge_coverage = edge_points / total_points
        center_coverage = center_points / total_points
        
        return edge_coverage, center_coverage
    
    def _analyze_road_network(self, selected_modules: List[RoadModule]) -> Dict[str, Any]:
        """
        Analyze the road network formed by selected modules.
        
        Args:
            selected_modules: List of selected modules
            
        Returns:
            Dictionary with network analysis results
        """
        if not selected_modules:
            return {
                'connected_components': [],
                'largest_component_size': 0,
                'network_diameter': 0,
                'average_path_length': 0
            }
        
        # Find connected components
        connected_components = self.constraint_manager._find_connected_components(selected_modules)
        
        # Analyze largest component
        largest_component = max(connected_components, key=len) if connected_components else []
        
        # Calculate network properties
        network_properties = self._calculate_network_properties(largest_component)
        
        return {
            'connected_components': [
                {
                    'size': len(component),
                    'modules': [m.id for m in component],
                    'total_area': sum(m.area for m in component)
                }
                for component in connected_components
            ],
            'num_components': len(connected_components),
            'largest_component_size': len(largest_component),
            'network_properties': network_properties
        }
    
    def _calculate_network_properties(self, modules: List[RoadModule]) -> Dict[str, float]:
        """
        Calculate network properties for a connected component.
        
        Args:
            modules: List of modules in the component
            
        Returns:
            Dictionary with network properties
        """
        if len(modules) <= 1:
            return {
                'diameter': 0,
                'average_path_length': 0,
                'clustering_coefficient': 0
            }
        
        # Build adjacency matrix
        n = len(modules)
        adj_matrix = np.zeros((n, n), dtype=bool)
        
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i != j and self.constraint_manager.check_modules_adjacent(module1, module2):
                    adj_matrix[i, j] = True
        
        # Calculate shortest paths using Floyd-Warshall
        dist_matrix = np.full((n, n), float('inf'))
        np.fill_diagonal(dist_matrix, 0)
        
        for i in range(n):
            for j in range(n):
                if adj_matrix[i, j]:
                    dist_matrix[i, j] = 1
        
        for k in range(n):
            for i in range(n):
                for j in range(n):
                    dist_matrix[i, j] = min(dist_matrix[i, j], 
                                          dist_matrix[i, k] + dist_matrix[k, j])
        
        # Calculate network metrics
        finite_distances = dist_matrix[dist_matrix != float('inf')]
        
        diameter = np.max(finite_distances) if len(finite_distances) > 0 else 0
        avg_path_length = np.mean(finite_distances) if len(finite_distances) > 0 else 0
        
        # Simple clustering coefficient
        clustering_coeff = np.sum(adj_matrix) / (n * (n - 1)) if n > 1 else 0
        
        return {
            'diameter': diameter,
            'average_path_length': avg_path_length,
            'clustering_coefficient': clustering_coeff
        }
    
    def _generate_layout_representation(self, selected_modules: List[RoadModule]) -> Dict[str, Any]:
        """
        Generate a layout representation for visualization.
        
        Args:
            selected_modules: List of selected modules
            
        Returns:
            Dictionary with layout representation
        """
        layout = {
            'parking_lot_bounds': {
                'width': self.config.width,
                'height': self.config.height
            },
            'obstacles': [
                {
                    'x': x, 'y': y, 'width': w, 'height': h
                }
                for x, y, w, h in self.config.obstacles
            ],
            'road_modules': [
                {
                    'id': module.id,
                    'type': module.module_type,
                    'orientation': module.orientation,
                    'rectangle': {
                        'x': module.rectangle.x,
                        'y': module.rectangle.y,
                        'width': module.rectangle.width,
                        'height': module.rectangle.height
                    },
                    'area': module.area,
                    'parking_accessibility': module.parking_accessibility_value
                }
                for module in selected_modules
            ]
        }
        
        return layout
    
    def _generate_solution_summary(self, selected_modules: List[RoadModule],
                                 validation_result: Dict[str, Any],
                                 performance_metrics: Dict[str, float]) -> Dict[str, Any]:
        """
        Generate a summary of the solution.
        
        Args:
            selected_modules: List of selected modules
            validation_result: Validation results
            performance_metrics: Performance metrics
            
        Returns:
            Dictionary with solution summary
        """
        return {
            'is_valid_solution': validation_result['is_valid'],
            'num_selected_modules': len(selected_modules),
            'total_road_area': performance_metrics['total_road_area'],
            'total_parking_spaces': performance_metrics['total_parking_spaces'],
            'area_efficiency': performance_metrics['area_efficiency'],
            'connectivity_score': performance_metrics['connectivity_ratio'],
            'space_utilization': performance_metrics['space_utilization'],
            'constraint_violations': len(validation_result.get('overlap_violations', [])),
            'network_connectivity': validation_result['num_connected_components'] == 1,
            'meets_minimum_requirements': (
                validation_result['min_spaces_satisfied'] and
                validation_result['area_constraint_satisfied']
            )
        }
    
    def compare_solutions(self, solutions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Compare multiple solutions and rank them.
        
        Args:
            solutions: List of processed solution dictionaries
            
        Returns:
            Dictionary with comparison results
        """
        if not solutions:
            return {'ranked_solutions': [], 'comparison_metrics': {}}
        
        # Extract metrics for comparison
        comparison_data = []
        for i, solution in enumerate(solutions):
            metrics = solution['performance_metrics']
            validation = solution['validation']
            
            comparison_data.append({
                'solution_index': i,
                'is_valid': validation['is_valid'],
                'total_parking_spaces': metrics['total_parking_spaces'],
                'area_efficiency': metrics['area_efficiency'],
                'connectivity_ratio': metrics['connectivity_ratio'],
                'space_utilization': metrics['space_utilization'],
                'constraint_violations': len(validation.get('overlap_violations', [])),
                'composite_score': self._calculate_composite_score(metrics, validation)
            })
        
        # Sort by composite score (higher is better)
        comparison_data.sort(key=lambda x: x['composite_score'], reverse=True)
        
        return {
            'ranked_solutions': comparison_data,
            'best_solution_index': comparison_data[0]['solution_index'],
            'comparison_metrics': {
                'num_valid_solutions': sum(1 for d in comparison_data if d['is_valid']),
                'best_parking_spaces': max(d['total_parking_spaces'] for d in comparison_data),
                'best_efficiency': max(d['area_efficiency'] for d in comparison_data),
                'best_connectivity': max(d['connectivity_ratio'] for d in comparison_data)
            }
        }
    
    def _calculate_composite_score(self, metrics: Dict[str, float], 
                                 validation: Dict[str, Any]) -> float:
        """
        Calculate a composite score for solution ranking.
        
        Args:
            metrics: Performance metrics
            validation: Validation results
            
        Returns:
            Composite score (higher is better)
        """
        # Base score from performance metrics
        score = (
            metrics['total_parking_spaces'] * 0.4 +
            metrics['area_efficiency'] * 0.3 +
            metrics['connectivity_ratio'] * 0.2 +
            (1.0 - metrics['space_utilization']) * 0.1  # Prefer less road area
        )
        
        # Penalty for invalid solutions
        if not validation['is_valid']:
            score *= 0.1  # Heavy penalty for constraint violations
        
        # Penalty for disconnected networks
        if validation['num_connected_components'] > 1:
            score *= 0.8
        
        return score
