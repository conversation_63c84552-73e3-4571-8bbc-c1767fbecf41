"""
Obstacle handling for parking lot optimization.

This module provides utilities for managing structural obstacles like
columns and walls in the parking lot layout optimization.
"""

from typing import List, Set, Tuple, Optional, Dict
import numpy as np
from ..core.config import ParkingLotConfig
from ..core.geometry import Rectangle, Point
from ..core.grid import GridManager


class ObstacleHandler:
    """
    Handles obstacle detection, validation, and spatial operations.
    
    This class provides utilities for working with structural obstacles
    in the parking lot and ensuring that road modules don't conflict
    with these constraints.
    """
    
    def __init__(self, config: ParkingLotConfig):
        """
        Initialize the obstacle handler.
        
        Args:
            config: Parking lot configuration containing obstacle definitions
        """
        self.config = config
        self.grid_manager = GridManager(config)
        self.obstacles = self._create_obstacle_rectangles()
    
    def _create_obstacle_rectangles(self) -> List[Rectangle]:
        """
        Create Rectangle objects for all obstacles.
        
        Returns:
            List of Rectangle objects representing obstacles
        """
        obstacles = []
        for x, y, width, height in self.config.obstacles:
            obstacles.append(Rectangle(x, y, width, height))
        return obstacles
    
    def check_module_obstacle_overlap(self, module_rect: Rectangle) -> bool:
        """
        Check if a module rectangle overlaps with any obstacles.
        
        Args:
            module_rect: Rectangle representing the road module
            
        Returns:
            True if module overlaps with obstacles, False otherwise
        """
        for obstacle in self.obstacles:
            if module_rect.overlaps_with(obstacle):
                return True
        return False
    
    def get_obstacle_overlap_area(self, module_rect: Rectangle) -> float:
        """
        Calculate total area of overlap between module and all obstacles.
        
        Args:
            module_rect: Rectangle representing the road module
            
        Returns:
            Total overlap area in square meters
        """
        total_overlap = 0.0
        for obstacle in self.obstacles:
            intersection = module_rect.intersection_with(obstacle)
            if intersection:
                total_overlap += intersection.area
        return total_overlap
    
    def get_overlapping_obstacles(self, module_rect: Rectangle) -> List[Rectangle]:
        """
        Get list of obstacles that overlap with a module.
        
        Args:
            module_rect: Rectangle representing the road module
            
        Returns:
            List of Rectangle objects for overlapping obstacles
        """
        overlapping = []
        for obstacle in self.obstacles:
            if module_rect.overlaps_with(obstacle):
                overlapping.append(obstacle)
        return overlapping
    
    def find_obstacle_free_regions(self) -> List[Rectangle]:
        """
        Find rectangular regions that are completely free of obstacles.
        
        This method uses a simplified approach to identify large obstacle-free
        areas that could be good candidates for road module placement.
        
        Returns:
            List of Rectangle objects representing obstacle-free regions
        """
        # Get the obstacle mask from grid manager
        obstacle_mask = self.grid_manager.obstacle_mask
        height_cells, width_cells = obstacle_mask.shape
        
        # Find connected components of free cells
        free_mask = ~obstacle_mask
        free_regions = []
        
        # Use a simple approach to find rectangular regions
        visited = np.zeros_like(free_mask, dtype=bool)
        
        for row in range(height_cells):
            for col in range(width_cells):
                if free_mask[row, col] and not visited[row, col]:
                    # Find the largest rectangle starting from this cell
                    rect_cells = self._find_largest_rectangle(
                        free_mask, visited, row, col
                    )
                    
                    if rect_cells:
                        # Convert to continuous coordinates
                        min_row = min(r for r, c in rect_cells)
                        max_row = max(r for r, c in rect_cells)
                        min_col = min(c for r, c in rect_cells)
                        max_col = max(c for r, c in rect_cells)
                        
                        x, y = self.grid_manager.grid_to_continuous(min_row, min_col)
                        width = (max_col - min_col + 1) * self.config.grid.cell_size
                        height = (max_row - min_row + 1) * self.config.grid.cell_size
                        
                        free_regions.append(Rectangle(x, y, width, height))
        
        return free_regions
    
    def _find_largest_rectangle(self, free_mask: np.ndarray, 
                              visited: np.ndarray,
                              start_row: int, start_col: int) -> Set[Tuple[int, int]]:
        """
        Find the largest rectangle of free cells starting from a given position.
        
        This is a simplified version that finds rectangular regions using
        a greedy approach.
        
        Args:
            free_mask: Boolean mask of free cells
            visited: Boolean mask of already processed cells
            start_row, start_col: Starting position
            
        Returns:
            Set of (row, col) tuples forming the rectangle
        """
        height_cells, width_cells = free_mask.shape
        
        if (start_row >= height_cells or start_col >= width_cells or
            visited[start_row, start_col] or not free_mask[start_row, start_col]):
            return set()
        
        # Find maximum width from starting position
        max_width = 0
        for col in range(start_col, width_cells):
            if free_mask[start_row, col] and not visited[start_row, col]:
                max_width += 1
            else:
                break
        
        # Find maximum height that maintains the width
        max_height = 0
        for row in range(start_row, height_cells):
            # Check if entire width is available at this row
            width_available = True
            for col in range(start_col, start_col + max_width):
                if (col >= width_cells or not free_mask[row, col] or 
                    visited[row, col]):
                    width_available = False
                    break
            
            if width_available:
                max_height += 1
            else:
                break
        
        # Create the rectangle if it's reasonably sized
        min_size = 3  # Minimum size in cells
        if max_width >= min_size and max_height >= min_size:
            rect_cells = set()
            for row in range(start_row, start_row + max_height):
                for col in range(start_col, start_col + max_width):
                    rect_cells.add((row, col))
                    visited[row, col] = True
            return rect_cells
        else:
            # Mark single cell as visited
            visited[start_row, start_col] = True
            return set()
    
    def calculate_clearance_distances(self, point: Point) -> Dict[str, float]:
        """
        Calculate distances from a point to the nearest obstacles.
        
        Args:
            point: Point to calculate clearances from
            
        Returns:
            Dictionary with clearance information
        """
        if not self.obstacles:
            return {
                'min_distance': float('inf'),
                'nearest_obstacle_id': None,
                'clearance_sufficient': True
            }
        
        min_distance = float('inf')
        nearest_obstacle_id = None
        
        for i, obstacle in enumerate(self.obstacles):
            # Calculate distance from point to obstacle rectangle
            distance = self._point_to_rectangle_distance(point, obstacle)
            
            if distance < min_distance:
                min_distance = distance
                nearest_obstacle_id = i
        
        # Check if clearance is sufficient (using car width as reference)
        clearance_sufficient = min_distance >= self.config.car.width
        
        return {
            'min_distance': min_distance,
            'nearest_obstacle_id': nearest_obstacle_id,
            'clearance_sufficient': clearance_sufficient
        }
    
    def _point_to_rectangle_distance(self, point: Point, rect: Rectangle) -> float:
        """
        Calculate the minimum distance from a point to a rectangle.
        
        Args:
            point: Point to calculate distance from
            rect: Rectangle to calculate distance to
            
        Returns:
            Minimum distance from point to rectangle
        """
        # If point is inside rectangle, distance is 0
        if rect.contains_point(point):
            return 0.0
        
        # Calculate distance to each edge
        dx = max(rect.left - point.x, 0, point.x - rect.right)
        dy = max(rect.bottom - point.y, 0, point.y - rect.top)
        
        return np.sqrt(dx**2 + dy**2)
    
    def validate_obstacle_configuration(self) -> List[str]:
        """
        Validate the obstacle configuration for potential issues.
        
        Returns:
            List of validation warnings/errors
        """
        issues = []
        
        # Check if obstacles are within parking lot bounds
        parking_lot = Rectangle(0, 0, self.config.width, self.config.height)
        
        for i, obstacle in enumerate(self.obstacles):
            if not (obstacle.left >= parking_lot.left and 
                   obstacle.right <= parking_lot.right and
                   obstacle.bottom >= parking_lot.bottom and
                   obstacle.top <= parking_lot.top):
                issues.append(f"Obstacle {i} extends beyond parking lot boundaries")
        
        # Check for overlapping obstacles
        for i, obs1 in enumerate(self.obstacles):
            for j, obs2 in enumerate(self.obstacles):
                if i < j and obs1.overlaps_with(obs2):
                    issues.append(f"Obstacles {i} and {j} overlap")
        
        # Check if obstacles block too much space
        total_obstacle_area = sum(obs.area for obs in self.obstacles)
        parking_lot_area = parking_lot.area
        obstacle_ratio = total_obstacle_area / parking_lot_area
        
        if obstacle_ratio > 0.3:  # More than 30% blocked
            issues.append(f"Obstacles block {obstacle_ratio:.1%} of parking lot area")
        
        # Check for obstacles that are too small (might be noise)
        min_obstacle_size = 0.5  # 0.5 square meters
        for i, obstacle in enumerate(self.obstacles):
            if obstacle.area < min_obstacle_size:
                issues.append(f"Obstacle {i} is very small ({obstacle.area:.2f} m²)")
        
        return issues
    
    def get_obstacle_statistics(self) -> Dict[str, any]:
        """
        Get statistics about the obstacles in the parking lot.
        
        Returns:
            Dictionary with obstacle statistics
        """
        if not self.obstacles:
            return {
                'num_obstacles': 0,
                'total_area': 0.0,
                'coverage_ratio': 0.0
            }
        
        total_area = sum(obs.area for obs in self.obstacles)
        parking_lot_area = self.config.width * self.config.height
        
        return {
            'num_obstacles': len(self.obstacles),
            'total_area': total_area,
            'coverage_ratio': total_area / parking_lot_area,
            'average_area': total_area / len(self.obstacles),
            'largest_obstacle': max(obs.area for obs in self.obstacles),
            'smallest_obstacle': min(obs.area for obs in self.obstacles),
            'obstacle_positions': [(obs.x, obs.y) for obs in self.obstacles],
            'obstacle_dimensions': [(obs.width, obs.height) for obs in self.obstacles]
        }
