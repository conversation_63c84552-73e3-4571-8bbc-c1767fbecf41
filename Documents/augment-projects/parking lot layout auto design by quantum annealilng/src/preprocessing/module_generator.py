"""
Module generation for parking lot road layout optimization.

This module generates candidate road modules that can be selected by the
quantum annealing algorithm to form the optimal road network.
"""

from dataclasses import dataclass
from typing import List, Set, Tuple, Dict, Optional
import numpy as np
from ..core.config import ParkingLotConfig
from ..core.geometry import Rectangle, Point, calculate_parking_spaces_around_module
from ..core.grid import GridManager


@dataclass
class RoadModule:
    """
    Represents a candidate road module.
    
    A road module is a rectangular section that can be selected to form
    part of the road network in the parking lot.
    """
    id: int
    rectangle: Rectangle
    module_type: str  # 'single_lane', 'double_lane', 'turning_area'
    orientation: str  # 'horizontal', 'vertical', 'square'
    covered_cells: Set[Tuple[int, int]]
    parking_accessibility_value: float
    adjacent_modules: Set[int]  # IDs of adjacent modules
    
    @property
    def area(self) -> float:
        """Get the area of the module."""
        return self.rectangle.area
    
    @property
    def center(self) -> Point:
        """Get the center point of the module."""
        return self.rectangle.center


class ModuleGenerator:
    """
    Generates candidate road modules for the parking lot optimization.
    
    This class implements Phase 1 of the algorithm: preprocessing and
    module generation with obstacle handling.
    """
    
    def __init__(self, config: ParkingLotConfig):
        """
        Initialize the module generator.
        
        Args:
            config: Parking lot configuration
        """
        self.config = config
        self.grid_manager = GridManager(config)
        self.modules: List[RoadModule] = []
        self.module_id_counter = 0
        
        # Calculate module specifications in grid cells
        self._calculate_module_specs()
    
    def _calculate_module_specs(self) -> None:
        """Calculate valid module specifications in grid cells."""
        cell_size = self.config.grid.cell_size
        
        # Single lane specifications
        self.single_lane_min_cells = int(self.config.road_modules.single_lane_min_width / cell_size)
        self.single_lane_max_cells = int(self.config.road_modules.single_lane_max_width / cell_size)
        
        # Double lane specifications  
        self.double_lane_min_cells = int(self.config.road_modules.double_lane_min_width / cell_size)
        self.double_lane_max_cells = int(self.config.road_modules.double_lane_max_width / cell_size)
        
        # Turning area specifications
        self.turning_area_min_cells = int(self.config.road_modules.turning_area_min_size / cell_size)
        
        # Module length specifications
        self.min_length_cells = int(self.config.module_generation.min_module_length / cell_size)
        self.max_length_cells = int(self.config.module_generation.max_module_length / cell_size)
    
    def generate_candidate_modules(self) -> List[RoadModule]:
        """
        Generate all candidate road modules for the parking lot.
        
        Returns:
            List of candidate RoadModule objects
        """
        self.modules = []
        self.module_id_counter = 0
        
        # Get available cells (non-obstacle)
        available_cells = self.grid_manager.get_available_cells()
        
        # Generate modules starting from each available cell
        for row, col in available_cells:
            self._generate_modules_from_position(row, col, available_cells)
        
        # Calculate adjacency relationships
        self._calculate_module_adjacencies()
        
        # Calculate parking accessibility values
        self._calculate_parking_accessibility()
        
        return self.modules
    
    def _generate_modules_from_position(self, start_row: int, start_col: int,
                                      available_cells: Set[Tuple[int, int]]) -> None:
        """
        Generate all valid modules starting from a given position.
        
        Args:
            start_row, start_col: Starting grid position
            available_cells: Set of available (non-obstacle) cells
        """
        # Generate horizontal modules
        self._generate_horizontal_modules(start_row, start_col, available_cells)
        
        # Generate vertical modules
        self._generate_vertical_modules(start_row, start_col, available_cells)
        
        # Generate square turning areas
        self._generate_turning_areas(start_row, start_col, available_cells)
    
    def _generate_horizontal_modules(self, start_row: int, start_col: int,
                                   available_cells: Set[Tuple[int, int]]) -> None:
        """Generate horizontal road modules."""
        # Try different widths (single lane, double lane)
        width_specs = [
            (self.single_lane_min_cells, self.single_lane_max_cells, 'single_lane'),
            (self.double_lane_min_cells, self.double_lane_max_cells, 'double_lane')
        ]
        
        for min_width, max_width, module_type in width_specs:
            for width in range(min_width, max_width + 1):
                # Try different lengths
                for length in range(self.min_length_cells, self.max_length_cells + 1):
                    # Check if all cells in the rectangle are available
                    cells = set()
                    valid = True
                    
                    for r in range(start_row, start_row + width):
                        for c in range(start_col, start_col + length):
                            if (r, c) not in available_cells:
                                valid = False
                                break
                            cells.add((r, c))
                        if not valid:
                            break
                    
                    if valid and len(cells) == width * length:
                        # Create the module
                        x, y = self.grid_manager.grid_to_continuous(start_row, start_col)
                        rect = Rectangle(x, y, 
                                       length * self.config.grid.cell_size,
                                       width * self.config.grid.cell_size)
                        
                        module = RoadModule(
                            id=self.module_id_counter,
                            rectangle=rect,
                            module_type=module_type,
                            orientation='horizontal',
                            covered_cells=cells,
                            parking_accessibility_value=0.0,  # Will be calculated later
                            adjacent_modules=set()
                        )
                        
                        self.modules.append(module)
                        self.module_id_counter += 1
    
    def _generate_vertical_modules(self, start_row: int, start_col: int,
                                 available_cells: Set[Tuple[int, int]]) -> None:
        """Generate vertical road modules."""
        # Try different widths (single lane, double lane)
        width_specs = [
            (self.single_lane_min_cells, self.single_lane_max_cells, 'single_lane'),
            (self.double_lane_min_cells, self.double_lane_max_cells, 'double_lane')
        ]
        
        for min_width, max_width, module_type in width_specs:
            for width in range(min_width, max_width + 1):
                # Try different lengths
                for length in range(self.min_length_cells, self.max_length_cells + 1):
                    # Check if all cells in the rectangle are available
                    cells = set()
                    valid = True
                    
                    for r in range(start_row, start_row + length):
                        for c in range(start_col, start_col + width):
                            if (r, c) not in available_cells:
                                valid = False
                                break
                            cells.add((r, c))
                        if not valid:
                            break
                    
                    if valid and len(cells) == width * length:
                        # Create the module
                        x, y = self.grid_manager.grid_to_continuous(start_row, start_col)
                        rect = Rectangle(x, y,
                                       width * self.config.grid.cell_size,
                                       length * self.config.grid.cell_size)
                        
                        module = RoadModule(
                            id=self.module_id_counter,
                            rectangle=rect,
                            module_type=module_type,
                            orientation='vertical',
                            covered_cells=cells,
                            parking_accessibility_value=0.0,  # Will be calculated later
                            adjacent_modules=set()
                        )
                        
                        self.modules.append(module)
                        self.module_id_counter += 1
    
    def _generate_turning_areas(self, start_row: int, start_col: int,
                              available_cells: Set[Tuple[int, int]]) -> None:
        """Generate square turning area modules."""
        # Try different sizes for turning areas
        max_size = min(self.turning_area_min_cells + 10, 
                      self.max_length_cells)  # Reasonable upper bound
        
        for size in range(self.turning_area_min_cells, max_size + 1):
            # Check if all cells in the square are available
            cells = set()
            valid = True
            
            for r in range(start_row, start_row + size):
                for c in range(start_col, start_col + size):
                    if (r, c) not in available_cells:
                        valid = False
                        break
                    cells.add((r, c))
                if not valid:
                    break
            
            if valid and len(cells) == size * size:
                # Create the module
                x, y = self.grid_manager.grid_to_continuous(start_row, start_col)
                side_length = size * self.config.grid.cell_size
                rect = Rectangle(x, y, side_length, side_length)
                
                module = RoadModule(
                    id=self.module_id_counter,
                    rectangle=rect,
                    module_type='turning_area',
                    orientation='square',
                    covered_cells=cells,
                    parking_accessibility_value=0.0,  # Will be calculated later
                    adjacent_modules=set()
                )
                
                self.modules.append(module)
                self.module_id_counter += 1
    
    def _calculate_module_adjacencies(self) -> None:
        """Calculate which modules are adjacent to each other."""
        for i, module1 in enumerate(self.modules):
            for j, module2 in enumerate(self.modules):
                if i != j and self._are_modules_adjacent(module1, module2):
                    module1.adjacent_modules.add(module2.id)
    
    def _are_modules_adjacent(self, module1: RoadModule, module2: RoadModule) -> bool:
        """
        Check if two modules are adjacent (share a border but don't overlap).
        
        Args:
            module1, module2: Modules to check
            
        Returns:
            True if modules are adjacent
        """
        # Check if modules overlap (they shouldn't be adjacent if they overlap)
        if module1.rectangle.overlaps_with(module2.rectangle):
            return False
        
        # Check if modules are close enough to be considered adjacent
        distance = self.grid_manager.calculate_cell_distances(
            module1.covered_cells, module2.covered_cells
        )
        
        # Adjacent if distance is 1 cell
        return distance <= 1.0
    
    def _calculate_parking_accessibility(self) -> None:
        """Calculate parking accessibility value for each module."""
        parking_lot_bounds = Rectangle(0, 0, self.config.width, self.config.height)
        
        for module in self.modules:
            # Calculate potential parking spaces around this module
            parking_spaces = calculate_parking_spaces_around_module(
                module.rectangle, 
                self.config.car,
                parking_lot_bounds
            )
            
            # Filter out parking spaces that overlap with obstacles
            valid_parking_spaces = []
            for space in parking_spaces:
                if self.grid_manager.check_rectangle_validity(space):
                    valid_parking_spaces.append(space)
            
            # Calculate accessibility value based on number and quality of parking spaces
            module.parking_accessibility_value = len(valid_parking_spaces)
    
    def get_module_statistics(self) -> Dict[str, any]:
        """
        Get statistics about the generated modules.
        
        Returns:
            Dictionary with module statistics
        """
        if not self.modules:
            return {}
        
        stats = {
            'total_modules': len(self.modules),
            'single_lane_modules': len([m for m in self.modules if m.module_type == 'single_lane']),
            'double_lane_modules': len([m for m in self.modules if m.module_type == 'double_lane']),
            'turning_area_modules': len([m for m in self.modules if m.module_type == 'turning_area']),
            'horizontal_modules': len([m for m in self.modules if m.orientation == 'horizontal']),
            'vertical_modules': len([m for m in self.modules if m.orientation == 'vertical']),
            'square_modules': len([m for m in self.modules if m.orientation == 'square']),
            'total_coverage_area': sum(m.area for m in self.modules),
            'average_accessibility': np.mean([m.parking_accessibility_value for m in self.modules]),
            'max_accessibility': max([m.parking_accessibility_value for m in self.modules]),
            'min_accessibility': min([m.parking_accessibility_value for m in self.modules])
        }
        
        return stats
    
    def filter_modules_by_criteria(self, min_accessibility: float = 0.0,
                                 module_types: Optional[List[str]] = None) -> List[RoadModule]:
        """
        Filter modules based on specified criteria.
        
        Args:
            min_accessibility: Minimum parking accessibility value
            module_types: List of module types to include
            
        Returns:
            Filtered list of modules
        """
        filtered_modules = []
        
        for module in self.modules:
            # Check accessibility threshold
            if module.parking_accessibility_value < min_accessibility:
                continue
                
            # Check module type
            if module_types and module.module_type not in module_types:
                continue
                
            filtered_modules.append(module)
        
        return filtered_modules
