"""
QUBO model formulation for parking lot optimization.

This module implements Phase 2 of the algorithm: QUBO model formulation
with overlap constraints, coverage optimization, connectivity rewards,
and area efficiency penalties.
"""

from typing import List, Dict, Tuple, Set
import numpy as np
from scipy.sparse import csr_matrix
import dimod

from ..core.config import ParkingLotConfig
from ..preprocessing.module_generator import RoadModule
from .constraints import ConstraintManager


class QUBOFormulator:
    """
    Formulates the QUBO (Quadratic Unconstrained Binary Optimization) model
    for parking lot road layout optimization.
    
    The QUBO objective function consists of four main components:
    1. H_overlap: Heavy penalty for overlapping modules (hard constraint)
    2. H_coverage: Reward modules that serve more parking spaces
    3. H_connectivity: Reward adjacent non-overlapping modules
    4. H_area: Penalize total road area for space efficiency
    """
    
    def __init__(self, modules: List[RoadModule], config: ParkingLotConfig):
        """
        Initialize the QUBO formulator.
        
        Args:
            modules: List of candidate road modules
            config: Parking lot configuration with QUBO weights
        """
        self.modules = modules
        self.config = config
        self.constraint_manager = ConstraintManager(modules, config)
        
        # Create module ID to index mapping
        self.module_id_to_index = {module.id: i for i, module in enumerate(modules)}
        self.num_modules = len(modules)
        
        # QUBO matrix (upper triangular)
        self.qubo_matrix = None
        self.linear_terms = None
        self.quadratic_terms = None
        
    def build_qubo_matrix(self) -> Dict[Tuple[int, int], float]:
        """
        Build the complete QUBO matrix for the optimization problem.
        
        The objective function is:
        H = H_overlap + H_coverage + H_connectivity + H_area
        
        Returns:
            Dictionary representing the QUBO matrix in dimod format
        """
        # Initialize QUBO components
        h_overlap = self._build_overlap_penalty()
        h_coverage = self._build_coverage_reward()
        h_connectivity = self._build_connectivity_reward()
        h_area = self._build_area_penalty()
        
        # Combine all components
        qubo_dict = {}
        
        # Add overlap penalties (quadratic terms)
        for (i, j), value in h_overlap.items():
            qubo_dict[(i, j)] = qubo_dict.get((i, j), 0.0) + value
        
        # Add coverage rewards (linear terms)
        for i, value in h_coverage.items():
            qubo_dict[(i, i)] = qubo_dict.get((i, i), 0.0) + value
        
        # Add connectivity rewards (quadratic terms)
        for (i, j), value in h_connectivity.items():
            qubo_dict[(i, j)] = qubo_dict.get((i, j), 0.0) + value
        
        # Add area penalties (linear terms)
        for i, value in h_area.items():
            qubo_dict[(i, i)] = qubo_dict.get((i, i), 0.0) + value
        
        # Store components for analysis
        self.linear_terms = {i: qubo_dict.get((i, i), 0.0) for i in range(self.num_modules)}
        self.quadratic_terms = {(i, j): v for (i, j), v in qubo_dict.items() if i != j}
        
        self.qubo_matrix = qubo_dict
        return qubo_dict
    
    def _build_overlap_penalty(self) -> Dict[Tuple[int, int], float]:
        """
        Build the overlap penalty component (H_overlap).
        
        H_overlap = P_overlap × Σ(y_k × y_j) for all k,j where C(k) ∩ C(j) ≠ ∅
        
        This creates heavy penalties for selecting overlapping modules.
        
        Returns:
            Dictionary with quadratic penalty terms
        """
        overlap_terms = {}
        penalty_weight = self.config.qubo_weights.overlap_penalty
        
        # Check all pairs of modules for overlap
        for i, module_i in enumerate(self.modules):
            for j, module_j in enumerate(self.modules):
                if i < j:  # Only upper triangular terms
                    if self.constraint_manager.check_modules_overlap(module_i, module_j):
                        # Add heavy penalty for selecting both overlapping modules
                        overlap_terms[(i, j)] = penalty_weight
        
        return overlap_terms
    
    def _build_coverage_reward(self) -> Dict[int, float]:
        """
        Build the coverage reward component (H_coverage).
        
        H_coverage = -A × Σ(w_k × y_k) where w_k is the parking accessibility value
        
        This rewards modules that provide access to more parking spaces.
        
        Returns:
            Dictionary with linear reward terms
        """
        coverage_terms = {}
        reward_weight = self.config.qubo_weights.coverage_reward
        
        for i, module in enumerate(self.modules):
            # Negative value because we want to maximize (minimize negative)
            coverage_terms[i] = -reward_weight * module.parking_accessibility_value
        
        return coverage_terms
    
    def _build_connectivity_reward(self) -> Dict[Tuple[int, int], float]:
        """
        Build the connectivity reward component (H_connectivity).
        
        H_connectivity = -B × Σ(y_k × y_j) for all adjacent modules k,j
        
        This rewards selecting adjacent modules to form connected road networks.
        
        Returns:
            Dictionary with quadratic reward terms
        """
        connectivity_terms = {}
        reward_weight = self.config.qubo_weights.connectivity_reward
        
        # Check all pairs of modules for adjacency
        for i, module_i in enumerate(self.modules):
            for j, module_j in enumerate(self.modules):
                if i < j:  # Only upper triangular terms
                    if self.constraint_manager.check_modules_adjacent(module_i, module_j):
                        # Add reward for selecting both adjacent modules
                        connectivity_terms[(i, j)] = -reward_weight
        
        return connectivity_terms
    
    def _build_area_penalty(self) -> Dict[int, float]:
        """
        Build the area penalty component (H_area).
        
        H_area = C × Σ(Area(k) × y_k)
        
        This penalizes the total road area to encourage space efficiency.
        
        Returns:
            Dictionary with linear penalty terms
        """
        area_terms = {}
        penalty_weight = self.config.qubo_weights.area_penalty
        
        for i, module in enumerate(self.modules):
            area_terms[i] = penalty_weight * module.area
        
        return area_terms
    
    def get_binary_quadratic_model(self) -> dimod.BinaryQuadraticModel:
        """
        Convert QUBO matrix to dimod BinaryQuadraticModel.
        
        Returns:
            BinaryQuadraticModel object for quantum annealing
        """
        if self.qubo_matrix is None:
            self.build_qubo_matrix()
        
        return dimod.BinaryQuadraticModel.from_qubo(self.qubo_matrix)
    
    def analyze_qubo_structure(self) -> Dict[str, any]:
        """
        Analyze the structure and properties of the QUBO matrix.
        
        Returns:
            Dictionary with QUBO analysis results
        """
        if self.qubo_matrix is None:
            self.build_qubo_matrix()
        
        # Count different types of terms
        linear_count = sum(1 for (i, j) in self.qubo_matrix.keys() if i == j)
        quadratic_count = sum(1 for (i, j) in self.qubo_matrix.keys() if i != j)
        
        # Analyze coefficient magnitudes
        all_coeffs = list(self.qubo_matrix.values())
        
        # Count positive and negative coefficients
        positive_coeffs = [c for c in all_coeffs if c > 0]
        negative_coeffs = [c for c in all_coeffs if c < 0]
        
        # Calculate density (fraction of possible terms that are non-zero)
        max_possible_terms = self.num_modules * (self.num_modules + 1) // 2
        density = len(self.qubo_matrix) / max_possible_terms
        
        analysis = {
            'num_variables': self.num_modules,
            'num_linear_terms': linear_count,
            'num_quadratic_terms': quadratic_count,
            'total_terms': len(self.qubo_matrix),
            'matrix_density': density,
            'coefficient_stats': {
                'min': min(all_coeffs) if all_coeffs else 0,
                'max': max(all_coeffs) if all_coeffs else 0,
                'mean': np.mean(all_coeffs) if all_coeffs else 0,
                'std': np.std(all_coeffs) if all_coeffs else 0
            },
            'positive_coefficients': len(positive_coeffs),
            'negative_coefficients': len(negative_coeffs),
            'weight_distribution': {
                'overlap_penalty_weight': self.config.qubo_weights.overlap_penalty,
                'coverage_reward_weight': self.config.qubo_weights.coverage_reward,
                'connectivity_reward_weight': self.config.qubo_weights.connectivity_reward,
                'area_penalty_weight': self.config.qubo_weights.area_penalty
            }
        }
        
        return analysis
    
    def validate_qubo_formulation(self) -> List[str]:
        """
        Validate the QUBO formulation for potential issues.
        
        Returns:
            List of validation warnings/errors
        """
        issues = []
        
        if self.qubo_matrix is None:
            self.build_qubo_matrix()
        
        # Check for extremely large coefficients that might cause numerical issues
        max_coeff = max(abs(c) for c in self.qubo_matrix.values())
        if max_coeff > 1e6:
            issues.append(f"Very large coefficient detected: {max_coeff}")
        
        # Check for zero coefficients (might indicate unused variables)
        zero_coeffs = sum(1 for c in self.qubo_matrix.values() if abs(c) < 1e-10)
        if zero_coeffs > 0:
            issues.append(f"{zero_coeffs} near-zero coefficients detected")
        
        # Check weight balance
        weights = self.config.qubo_weights
        if weights.overlap_penalty < 10 * max(weights.coverage_reward, 
                                            weights.connectivity_reward,
                                            weights.area_penalty):
            issues.append("Overlap penalty might be too small relative to other weights")
        
        # Check for disconnected variables (no quadratic terms)
        variables_in_quadratic = set()
        for i, j in self.quadratic_terms.keys():
            variables_in_quadratic.add(i)
            variables_in_quadratic.add(j)
        
        disconnected_vars = set(range(self.num_modules)) - variables_in_quadratic
        if disconnected_vars:
            issues.append(f"{len(disconnected_vars)} variables have no quadratic interactions")
        
        return issues
    
    def get_solution_interpretation(self, solution: Dict[int, int]) -> Dict[str, any]:
        """
        Interpret a solution from the quantum annealer.
        
        Args:
            solution: Dictionary mapping variable indices to binary values
            
        Returns:
            Dictionary with solution interpretation
        """
        selected_modules = []
        total_area = 0.0
        total_accessibility = 0.0
        
        for var_index, value in solution.items():
            if value == 1 and var_index < len(self.modules):
                module = self.modules[var_index]
                selected_modules.append(module)
                total_area += module.area
                total_accessibility += module.parking_accessibility_value
        
        # Check for overlaps in solution
        overlaps = []
        for i, module_i in enumerate(selected_modules):
            for j, module_j in enumerate(selected_modules):
                if i < j and self.constraint_manager.check_modules_overlap(module_i, module_j):
                    overlaps.append((module_i.id, module_j.id))
        
        # Calculate connectivity
        connected_pairs = 0
        for i, module_i in enumerate(selected_modules):
            for j, module_j in enumerate(selected_modules):
                if i < j and self.constraint_manager.check_modules_adjacent(module_i, module_j):
                    connected_pairs += 1
        
        return {
            'selected_modules': selected_modules,
            'num_selected': len(selected_modules),
            'total_road_area': total_area,
            'total_accessibility': total_accessibility,
            'num_overlaps': len(overlaps),
            'overlapping_pairs': overlaps,
            'connected_pairs': connected_pairs,
            'solution_valid': len(overlaps) == 0,
            'area_efficiency': total_accessibility / total_area if total_area > 0 else 0
        }
