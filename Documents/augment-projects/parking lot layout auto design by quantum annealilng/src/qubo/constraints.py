"""
Constraint management for QUBO formulation.

This module provides utilities for checking constraints and relationships
between road modules in the parking lot optimization problem.
"""

from typing import List, Set, Tuple, Dict
import numpy as np
from ..core.config import ParkingLotConfig
from ..core.geometry import Rectangle, calculate_rectangle_overlap_area
from ..preprocessing.module_generator import RoadModule


class ConstraintManager:
    """
    Manages constraints and relationships between road modules.
    
    This class provides utilities for checking overlaps, adjacencies,
    and other spatial relationships that are crucial for the QUBO
    formulation.
    """
    
    def __init__(self, modules: List[RoadModule], config: ParkingLotConfig):
        """
        Initialize the constraint manager.
        
        Args:
            modules: List of candidate road modules
            config: Parking lot configuration
        """
        self.modules = modules
        self.config = config
        
        # Pre-compute overlap and adjacency matrices for efficiency
        self.overlap_matrix = self._compute_overlap_matrix()
        self.adjacency_matrix = self._compute_adjacency_matrix()
        
    def _compute_overlap_matrix(self) -> np.ndarray:
        """
        Pre-compute overlap relationships between all module pairs.
        
        Returns:
            Boolean matrix where entry (i,j) is True if modules i and j overlap
        """
        n = len(self.modules)
        overlap_matrix = np.zeros((n, n), dtype=bool)
        
        for i in range(n):
            for j in range(i + 1, n):
                if self.modules[i].rectangle.overlaps_with(self.modules[j].rectangle):
                    overlap_matrix[i, j] = True
                    overlap_matrix[j, i] = True
                    
        return overlap_matrix
    
    def _compute_adjacency_matrix(self) -> np.ndarray:
        """
        Pre-compute adjacency relationships between all module pairs.
        
        Returns:
            Boolean matrix where entry (i,j) is True if modules i and j are adjacent
        """
        n = len(self.modules)
        adjacency_matrix = np.zeros((n, n), dtype=bool)
        
        for i in range(n):
            for j in range(i + 1, n):
                if self._are_modules_adjacent(self.modules[i], self.modules[j]):
                    adjacency_matrix[i, j] = True
                    adjacency_matrix[j, i] = True
                    
        return adjacency_matrix
    
    def check_modules_overlap(self, module1: RoadModule, module2: RoadModule) -> bool:
        """
        Check if two modules overlap.
        
        Args:
            module1, module2: Modules to check
            
        Returns:
            True if modules overlap
        """
        # Find indices of modules
        idx1 = next(i for i, m in enumerate(self.modules) if m.id == module1.id)
        idx2 = next(i for i, m in enumerate(self.modules) if m.id == module2.id)
        
        return self.overlap_matrix[idx1, idx2]
    
    def check_modules_adjacent(self, module1: RoadModule, module2: RoadModule) -> bool:
        """
        Check if two modules are adjacent.
        
        Args:
            module1, module2: Modules to check
            
        Returns:
            True if modules are adjacent
        """
        # Find indices of modules
        idx1 = next(i for i, m in enumerate(self.modules) if m.id == module1.id)
        idx2 = next(i for i, m in enumerate(self.modules) if m.id == module2.id)
        
        return self.adjacency_matrix[idx1, idx2]
    
    def _are_modules_adjacent(self, module1: RoadModule, module2: RoadModule) -> bool:
        """
        Determine if two modules are adjacent (share a border but don't overlap).
        
        Args:
            module1, module2: Modules to check
            
        Returns:
            True if modules are adjacent
        """
        rect1 = module1.rectangle
        rect2 = module2.rectangle
        
        # Modules can't be adjacent if they overlap
        if rect1.overlaps_with(rect2):
            return False
        
        # Check if rectangles share a border
        # Horizontal adjacency (share vertical border)
        horizontal_adjacent = (
            (abs(rect1.right - rect2.left) < 1e-6 or abs(rect2.right - rect1.left) < 1e-6) and
            not (rect1.top <= rect2.bottom or rect2.top <= rect1.bottom)
        )
        
        # Vertical adjacency (share horizontal border)
        vertical_adjacent = (
            (abs(rect1.top - rect2.bottom) < 1e-6 or abs(rect2.top - rect1.bottom) < 1e-6) and
            not (rect1.right <= rect2.left or rect2.right <= rect1.left)
        )
        
        return horizontal_adjacent or vertical_adjacent
    
    def get_overlapping_module_pairs(self) -> List[Tuple[int, int]]:
        """
        Get all pairs of modules that overlap.
        
        Returns:
            List of (module_id1, module_id2) tuples for overlapping modules
        """
        overlapping_pairs = []
        n = len(self.modules)
        
        for i in range(n):
            for j in range(i + 1, n):
                if self.overlap_matrix[i, j]:
                    overlapping_pairs.append((self.modules[i].id, self.modules[j].id))
                    
        return overlapping_pairs
    
    def get_adjacent_module_pairs(self) -> List[Tuple[int, int]]:
        """
        Get all pairs of modules that are adjacent.
        
        Returns:
            List of (module_id1, module_id2) tuples for adjacent modules
        """
        adjacent_pairs = []
        n = len(self.modules)
        
        for i in range(n):
            for j in range(i + 1, n):
                if self.adjacency_matrix[i, j]:
                    adjacent_pairs.append((self.modules[i].id, self.modules[j].id))
                    
        return adjacent_pairs
    
    def validate_solution(self, selected_module_ids: Set[int]) -> Dict[str, any]:
        """
        Validate a solution by checking all constraints.
        
        Args:
            selected_module_ids: Set of selected module IDs
            
        Returns:
            Dictionary with validation results
        """
        # Get selected modules
        selected_modules = [m for m in self.modules if m.id in selected_module_ids]
        
        # Check for overlaps
        overlap_violations = []
        for i, module1 in enumerate(selected_modules):
            for j, module2 in enumerate(selected_modules):
                if i < j and self.check_modules_overlap(module1, module2):
                    overlap_violations.append((module1.id, module2.id))
        
        # Check connectivity
        connected_components = self._find_connected_components(selected_modules)
        
        # Calculate total metrics
        total_area = sum(m.area for m in selected_modules)
        total_accessibility = sum(m.parking_accessibility_value for m in selected_modules)
        
        # Check area constraint
        parking_lot_area = self.config.width * self.config.height
        road_ratio = total_area / parking_lot_area
        area_constraint_satisfied = road_ratio <= self.config.validation.max_road_ratio
        
        # Check minimum parking spaces
        min_spaces_satisfied = total_accessibility >= self.config.validation.min_parking_spaces
        
        return {
            'is_valid': len(overlap_violations) == 0,
            'overlap_violations': overlap_violations,
            'num_connected_components': len(connected_components),
            'largest_component_size': max(len(comp) for comp in connected_components) if connected_components else 0,
            'total_road_area': total_area,
            'road_area_ratio': road_ratio,
            'area_constraint_satisfied': area_constraint_satisfied,
            'total_parking_accessibility': total_accessibility,
            'min_spaces_satisfied': min_spaces_satisfied,
            'selected_module_count': len(selected_modules),
            'connectivity_score': self._calculate_connectivity_score(selected_modules)
        }
    
    def _find_connected_components(self, modules: List[RoadModule]) -> List[List[RoadModule]]:
        """
        Find connected components in the selected modules.
        
        Args:
            modules: List of selected modules
            
        Returns:
            List of connected components (each is a list of modules)
        """
        if not modules:
            return []
        
        # Create adjacency list for selected modules
        module_to_index = {m.id: i for i, m in enumerate(modules)}
        adjacency_list = {i: [] for i in range(len(modules))}
        
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i != j and self.check_modules_adjacent(module1, module2):
                    adjacency_list[i].append(j)
        
        # Find connected components using DFS
        visited = set()
        components = []
        
        for i in range(len(modules)):
            if i not in visited:
                component = []
                stack = [i]
                
                while stack:
                    current = stack.pop()
                    if current not in visited:
                        visited.add(current)
                        component.append(modules[current])
                        
                        for neighbor in adjacency_list[current]:
                            if neighbor not in visited:
                                stack.append(neighbor)
                
                if component:
                    components.append(component)
        
        return components
    
    def _calculate_connectivity_score(self, modules: List[RoadModule]) -> float:
        """
        Calculate a connectivity score for the selected modules.
        
        Args:
            modules: List of selected modules
            
        Returns:
            Connectivity score (higher is better)
        """
        if len(modules) <= 1:
            return 0.0
        
        # Count adjacent pairs
        adjacent_pairs = 0
        total_pairs = len(modules) * (len(modules) - 1) // 2
        
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i < j and self.check_modules_adjacent(module1, module2):
                    adjacent_pairs += 1
        
        # Connectivity score as ratio of adjacent pairs to total possible pairs
        return adjacent_pairs / total_pairs if total_pairs > 0 else 0.0
    
    def get_constraint_statistics(self) -> Dict[str, any]:
        """
        Get statistics about constraints and relationships.
        
        Returns:
            Dictionary with constraint statistics
        """
        n = len(self.modules)
        
        # Count overlaps and adjacencies
        total_overlaps = np.sum(self.overlap_matrix) // 2  # Divide by 2 since matrix is symmetric
        total_adjacencies = np.sum(self.adjacency_matrix) // 2
        
        # Calculate densities
        max_possible_pairs = n * (n - 1) // 2
        overlap_density = total_overlaps / max_possible_pairs if max_possible_pairs > 0 else 0
        adjacency_density = total_adjacencies / max_possible_pairs if max_possible_pairs > 0 else 0
        
        return {
            'num_modules': n,
            'total_overlapping_pairs': int(total_overlaps),
            'total_adjacent_pairs': int(total_adjacencies),
            'overlap_density': overlap_density,
            'adjacency_density': adjacency_density,
            'max_possible_pairs': max_possible_pairs,
            'constraint_matrix_sparsity': {
                'overlap_matrix_nonzeros': int(np.sum(self.overlap_matrix)),
                'adjacency_matrix_nonzeros': int(np.sum(self.adjacency_matrix))
            }
        }
    
    def find_conflicting_modules(self, module_id: int) -> Dict[str, List[int]]:
        """
        Find all modules that conflict with a given module.
        
        Args:
            module_id: ID of the module to check
            
        Returns:
            Dictionary with overlapping and adjacent module IDs
        """
        module_index = next(i for i, m in enumerate(self.modules) if m.id == module_id)
        
        overlapping = []
        adjacent = []
        
        for i, module in enumerate(self.modules):
            if i != module_index:
                if self.overlap_matrix[module_index, i]:
                    overlapping.append(module.id)
                elif self.adjacency_matrix[module_index, i]:
                    adjacent.append(module.id)
        
        return {
            'overlapping_modules': overlapping,
            'adjacent_modules': adjacent
        }
