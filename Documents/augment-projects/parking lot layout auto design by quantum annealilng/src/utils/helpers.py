"""
Helper utilities for parking lot optimization.

This module provides various utility functions used throughout the project.
"""

import time
import logging
import psutil
import os
from functools import wraps
from typing import Any, Callable, Dict, List
from ..core.config import ParkingLotConfig


def setup_logging(level: str = "INFO", log_file: str = None) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        level: Logging level ("DEBUG", "INFO", "WARNING", "ERROR")
        log_file: Optional log file path
        
    Returns:
        Configured logger instance
    """
    # Convert string level to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set up root logger
    logger = logging.getLogger('parking_optimization')
    logger.setLevel(numeric_level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def timer(func: Callable) -> Callable:
    """
    Decorator to measure function execution time.
    
    Args:
        func: Function to time
        
    Returns:
        Wrapped function that logs execution time
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger = logging.getLogger('parking_optimization')
        logger.info(f"{func.__name__} executed in {end_time - start_time:.4f} seconds")
        
        return result
    return wrapper


def memory_usage() -> Dict[str, float]:
    """
    Get current memory usage statistics.
    
    Returns:
        Dictionary with memory usage information
    """
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    return {
        'rss_mb': memory_info.rss / 1024 / 1024,  # Resident Set Size in MB
        'vms_mb': memory_info.vms / 1024 / 1024,  # Virtual Memory Size in MB
        'percent': process.memory_percent(),       # Percentage of total system memory
        'available_mb': psutil.virtual_memory().available / 1024 / 1024
    }


def validate_config(config: ParkingLotConfig) -> List[str]:
    """
    Validate parking lot configuration.
    
    Args:
        config: Configuration to validate
        
    Returns:
        List of validation error messages
    """
    errors = config.validate()
    
    # Additional validation checks
    if config.grid.cell_size > min(config.car.width, config.car.length) / 2:
        errors.append("Grid cell size is too large relative to car dimensions")
    
    if config.qubo_weights.overlap_penalty <= 0:
        errors.append("Overlap penalty must be positive")
    
    if config.quantum_annealing.num_reads <= 0:
        errors.append("Number of reads must be positive")
    
    return errors


def create_test_config(width: float = 50.0, height: float = 30.0,
                      obstacles: List[tuple] = None) -> ParkingLotConfig:
    """
    Create a test configuration for development and testing.
    
    Args:
        width: Parking lot width in meters
        height: Parking lot height in meters
        obstacles: List of obstacle tuples (x, y, width, height)
        
    Returns:
        Test configuration
    """
    if obstacles is None:
        obstacles = [
            (10.0, 10.0, 2.0, 2.0),  # Column 1
            (25.0, 15.0, 1.5, 1.5),  # Column 2
            (40.0, 8.0, 2.0, 2.0)    # Column 3
        ]
    
    return ParkingLotConfig(
        width=width,
        height=height,
        obstacles=obstacles
    )


def print_solution_summary(solution: Dict[str, Any]) -> None:
    """
    Print a formatted summary of a solution.
    
    Args:
        solution: Solution dictionary from post-processor
    """
    summary = solution.get('solution_summary', {})
    metrics = solution.get('performance_metrics', {})
    
    print("\n" + "="*50)
    print("PARKING LOT OPTIMIZATION SOLUTION SUMMARY")
    print("="*50)
    
    print(f"Valid Solution: {'✓' if summary.get('is_valid_solution', False) else '✗'}")
    print(f"Selected Modules: {summary.get('num_selected_modules', 0)}")
    print(f"Total Parking Spaces: {metrics.get('total_parking_spaces', 0):.0f}")
    print(f"Total Road Area: {metrics.get('total_road_area', 0):.2f} m²")
    print(f"Area Efficiency: {metrics.get('area_efficiency', 0):.3f} spaces/m²")
    print(f"Connectivity Score: {metrics.get('connectivity_ratio', 0):.1%}")
    print(f"Space Utilization: {metrics.get('space_utilization', 0):.1%}")
    
    if summary.get('constraint_violations', 0) > 0:
        print(f"⚠️  Constraint Violations: {summary.get('constraint_violations', 0)}")
    
    if not summary.get('network_connectivity', True):
        print("⚠️  Network is not fully connected")
    
    print("="*50)


def save_solution_to_file(solution: Dict[str, Any], filename: str) -> None:
    """
    Save solution to a JSON file.
    
    Args:
        solution: Solution dictionary
        filename: Output filename
    """
    import json
    
    # Convert numpy arrays and other non-serializable objects
    def convert_for_json(obj):
        if hasattr(obj, 'tolist'):  # numpy arrays
            return obj.tolist()
        elif hasattr(obj, '__dict__'):  # custom objects
            return obj.__dict__
        else:
            return str(obj)
    
    # Create a serializable version of the solution
    serializable_solution = {}
    for key, value in solution.items():
        if key == 'selected_modules':
            # Convert modules to dictionaries
            serializable_solution[key] = [
                {
                    'id': module.id,
                    'type': module.module_type,
                    'orientation': module.orientation,
                    'rectangle': {
                        'x': module.rectangle.x,
                        'y': module.rectangle.y,
                        'width': module.rectangle.width,
                        'height': module.rectangle.height
                    },
                    'area': module.area,
                    'parking_accessibility': module.parking_accessibility_value
                }
                for module in value
            ]
        else:
            try:
                json.dumps(value)  # Test if directly serializable
                serializable_solution[key] = value
            except (TypeError, ValueError):
                serializable_solution[key] = convert_for_json(value)
    
    with open(filename, 'w') as f:
        json.dump(serializable_solution, f, indent=2, default=convert_for_json)
    
    print(f"Solution saved to {filename}")


def load_solution_from_file(filename: str) -> Dict[str, Any]:
    """
    Load solution from a JSON file.
    
    Args:
        filename: Input filename
        
    Returns:
        Solution dictionary
    """
    import json
    
    with open(filename, 'r') as f:
        solution = json.load(f)
    
    print(f"Solution loaded from {filename}")
    return solution


def calculate_parking_lot_statistics(config: ParkingLotConfig) -> Dict[str, Any]:
    """
    Calculate basic statistics about the parking lot configuration.
    
    Args:
        config: Parking lot configuration
        
    Returns:
        Dictionary with statistics
    """
    total_area = config.width * config.height
    obstacle_area = sum(w * h for x, y, w, h in config.obstacles)
    available_area = total_area - obstacle_area
    
    # Estimate maximum theoretical parking spaces
    car_total_width = config.car.width + 2 * config.car.clearance
    car_total_length = config.car.length + 2 * config.car.clearance
    car_area = car_total_width * car_total_length
    max_theoretical_spaces = int(available_area / car_area)
    
    # Grid statistics
    grid_cells_total = int(config.width / config.grid.cell_size) * int(config.height / config.grid.cell_size)
    
    return {
        'total_area_m2': total_area,
        'obstacle_area_m2': obstacle_area,
        'available_area_m2': available_area,
        'obstacle_ratio': obstacle_area / total_area,
        'max_theoretical_parking_spaces': max_theoretical_spaces,
        'car_area_m2': car_area,
        'grid_cells_total': grid_cells_total,
        'grid_cell_area_m2': config.grid.cell_size ** 2,
        'parking_lot_dimensions': (config.width, config.height),
        'num_obstacles': len(config.obstacles)
    }


def benchmark_solver_performance(solver_func: Callable, 
                               test_cases: List[Any],
                               num_runs: int = 5) -> Dict[str, Any]:
    """
    Benchmark solver performance across multiple test cases.
    
    Args:
        solver_func: Solver function to benchmark
        test_cases: List of test cases to run
        num_runs: Number of runs per test case
        
    Returns:
        Benchmark results
    """
    results = {
        'test_cases': [],
        'average_time': 0.0,
        'total_runs': 0,
        'memory_usage': []
    }
    
    total_time = 0.0
    total_runs = 0
    
    for i, test_case in enumerate(test_cases):
        case_times = []
        case_memory = []
        
        for run in range(num_runs):
            # Measure memory before
            mem_before = memory_usage()
            
            # Time the solver
            start_time = time.time()
            result = solver_func(test_case)
            end_time = time.time()
            
            # Measure memory after
            mem_after = memory_usage()
            
            run_time = end_time - start_time
            case_times.append(run_time)
            case_memory.append(mem_after['rss_mb'] - mem_before['rss_mb'])
            
            total_time += run_time
            total_runs += 1
        
        results['test_cases'].append({
            'case_index': i,
            'average_time': sum(case_times) / len(case_times),
            'min_time': min(case_times),
            'max_time': max(case_times),
            'std_time': (sum((t - sum(case_times)/len(case_times))**2 for t in case_times) / len(case_times))**0.5,
            'average_memory_delta': sum(case_memory) / len(case_memory)
        })
    
    results['average_time'] = total_time / total_runs
    results['total_runs'] = total_runs
    
    return results
