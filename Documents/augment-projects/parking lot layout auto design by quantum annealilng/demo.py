#!/usr/bin/env python3
"""
Quick demonstration of the quantum annealing parking lot optimization system.

This script provides a simplified demonstration with a smaller problem size
for faster execution and easier understanding.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent / "src"))

from src.core.config import ParkingLotConfig
from src.preprocessing.module_generator import ModuleGenerator
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer
from src.solver.postprocessor import SolutionPostProcessor
from src.utils.helpers import print_solution_summary


def run_demo():
    """Run a quick demonstration of the system."""
    
    print("🚗 Quantum Annealing Parking Lot Optimization Demo")
    print("=" * 55)
    
    # Create a small parking lot for quick demonstration
    config = ParkingLotConfig(
        width=25.0,  # 25m x 15m parking lot
        height=15.0,
        obstacles=[
            (10.0, 6.0, 2.0, 2.0),  # Single obstacle
        ]
    )
    
    print(f"📐 Parking lot: {config.width}m x {config.height}m")
    print(f"🚧 Obstacles: {len(config.obstacles)}")
    
    # Phase 1: Generate candidate modules
    print("\n🔧 Phase 1: Generating candidate road modules...")
    generator = ModuleGenerator(config)
    modules = generator.generate_candidate_modules()
    
    module_stats = generator.get_module_statistics()
    print(f"✅ Generated {module_stats['total_modules']} candidate modules")
    print(f"   - Single lane: {module_stats['single_lane_modules']}")
    print(f"   - Double lane: {module_stats['double_lane_modules']}")
    print(f"   - Turning areas: {module_stats['turning_area_modules']}")
    
    if len(modules) == 0:
        print("❌ No modules generated! Try a larger parking lot.")
        return
    
    # Limit modules for faster processing
    if len(modules) > 50:
        print(f"🔄 Limiting to 50 modules for faster processing...")
        modules = modules[:50]
    
    # Phase 2: Formulate QUBO problem
    print("\n🧮 Phase 2: Formulating QUBO problem...")
    formulator = QUBOFormulator(modules, config)
    qubo_matrix = formulator.build_qubo_matrix()
    
    qubo_analysis = formulator.analyze_qubo_structure()
    print(f"✅ QUBO matrix: {qubo_analysis['num_variables']} variables, "
          f"{qubo_analysis['total_terms']} terms")
    print(f"   Matrix density: {qubo_analysis['matrix_density']:.3f}")
    
    # Phase 3: Solve with quantum annealing
    print("\n⚛️  Phase 3: Solving with quantum annealing...")
    solver = QuantumAnnealer(config)
    
    # Use fewer reads for faster demo
    solution_result = solver.solve_with_formulator(formulator, num_reads=500)
    
    print(f"✅ Best energy: {solution_result['energy']:.2f}")
    print(f"   Solver: {solution_result['solver_info']['sampler_type']}")
    
    # Post-process solution
    print("\n📊 Post-processing solution...")
    postprocessor = SolutionPostProcessor(modules, config)
    processed_solution = postprocessor.process_solution(solution_result['solution'])
    
    # Display results
    print_solution_summary(processed_solution)
    
    # Additional analysis
    validation = processed_solution['validation']
    metrics = processed_solution['performance_metrics']
    
    print("\n📈 Detailed Analysis:")
    print(f"   Valid solution: {'✅ Yes' if validation['is_valid'] else '❌ No'}")
    print(f"   Selected modules: {len(processed_solution['selected_modules'])}")
    print(f"   Parking spaces: {metrics['total_parking_spaces']:.0f}")
    print(f"   Road area: {metrics['total_road_area']:.1f} m²")
    print(f"   Area efficiency: {metrics['area_efficiency']:.3f} spaces/m²")
    print(f"   Connectivity: {metrics['connectivity_ratio']:.1%}")
    
    if validation['overlap_violations']:
        print(f"   ⚠️  Overlap violations: {len(validation['overlap_violations'])}")
    
    print("\n🎯 Solution Quality:")
    if validation['is_valid']:
        print("   ✅ Solution satisfies all constraints")
    else:
        print("   ⚠️  Solution has constraint violations")
    
    if metrics['connectivity_ratio'] > 0.5:
        print("   ✅ Good road network connectivity")
    else:
        print("   ⚠️  Poor road network connectivity")
    
    if metrics['area_efficiency'] > 0.1:
        print("   ✅ Efficient use of space")
    else:
        print("   ⚠️  Inefficient space utilization")
    
    print("\n🏁 Demo completed successfully!")
    print("\nNext steps:")
    print("   • Run 'python main.py' for full optimization with visualization")
    print("   • Try 'python examples/basic_layout.py' for more examples")
    print("   • Modify config parameters to test different scenarios")
    
    return processed_solution


if __name__ == "__main__":
    try:
        solution = run_demo()
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error during demo: {e}")
        print("This might be due to missing dependencies or configuration issues.")
        print("Please check the README.md for installation instructions.")
