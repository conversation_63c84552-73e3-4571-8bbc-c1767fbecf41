#!/usr/bin/env python3
"""
Basic parking lot layout optimization example.

This example demonstrates the basic usage of the quantum annealing
parking lot optimization system with a simple configuration.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.config import ParkingLotConfig
from src.preprocessing.module_generator import ModuleGenerator
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer
from src.solver.postprocessor import SolutionPostProcessor
from src.visualization.layout_plotter import LayoutPlotter
from src.visualization.metrics_analyzer import MetricsAnalyzer
from src.utils.helpers import setup_logging, print_solution_summary, timer
import matplotlib.pyplot as plt


@timer
def run_basic_optimization():
    """Run a basic parking lot optimization example."""
    
    # Set up logging
    logger = setup_logging("INFO")
    logger.info("Starting basic parking lot optimization example")
    
    # Create a simple parking lot configuration
    config = ParkingLotConfig(
        width=40.0,  # 40m x 25m parking lot
        height=25.0,
        obstacles=[
            (15.0, 10.0, 2.0, 2.0),  # Single column obstacle
            (30.0, 8.0, 1.5, 1.5)   # Smaller obstacle
        ]
    )
    
    logger.info(f"Parking lot: {config.width}m x {config.height}m")
    logger.info(f"Obstacles: {len(config.obstacles)}")
    
    # Generate candidate modules
    logger.info("Generating candidate road modules...")
    generator = ModuleGenerator(config)
    modules = generator.generate_candidate_modules()
    
    module_stats = generator.get_module_statistics()
    logger.info(f"Generated {module_stats['total_modules']} candidate modules")
    logger.info(f"Single lane: {module_stats['single_lane_modules']}, "
               f"Double lane: {module_stats['double_lane_modules']}, "
               f"Turning areas: {module_stats['turning_area_modules']}")
    
    # Formulate QUBO problem
    logger.info("Formulating QUBO problem...")
    formulator = QUBOFormulator(modules, config)
    qubo_analysis = formulator.analyze_qubo_structure()
    
    logger.info(f"QUBO matrix: {qubo_analysis['num_variables']} variables, "
               f"{qubo_analysis['total_terms']} terms")
    logger.info(f"Matrix density: {qubo_analysis['matrix_density']:.3f}")
    
    # Solve using quantum annealing
    logger.info("Solving with quantum annealing...")
    solver = QuantumAnnealer(config)
    solution_result = solver.solve_with_formulator(formulator)
    
    logger.info(f"Best energy: {solution_result['energy']:.2f}")
    logger.info(f"Solver: {solution_result['solver_info']['sampler_type']}")
    
    # Post-process solution
    logger.info("Post-processing solution...")
    postprocessor = SolutionPostProcessor(modules, config)
    processed_solution = postprocessor.process_solution(solution_result['solution'])
    
    # Print summary
    print_solution_summary(processed_solution)
    
    # Create visualizations
    logger.info("Creating visualizations...")
    plotter = LayoutPlotter(config)
    
    # Plot the solution
    fig1, ax = plt.subplots(figsize=(12, 8))
    plotter.plot_solution(
        processed_solution['selected_modules'],
        all_modules=modules,
        ax=ax,
        show_accessibility=True
    )
    plt.title("Optimized Parking Lot Layout")
    plt.tight_layout()
    
    # Plot performance metrics
    analyzer = MetricsAnalyzer(config)
    fig2 = analyzer.plot_solution_metrics([processed_solution])
    
    # Show plots
    plt.show()
    
    # Save results
    plotter.save_plot(fig1, "basic_layout_solution.png")
    plotter.save_plot(fig2, "basic_layout_metrics.png")
    
    logger.info("Basic optimization example completed successfully!")
    
    return processed_solution


def demonstrate_parameter_sensitivity():
    """Demonstrate parameter sensitivity analysis."""
    
    logger = setup_logging("INFO")
    logger.info("Running parameter sensitivity analysis...")
    
    # Base configuration
    base_config = ParkingLotConfig(
        width=30.0,
        height=20.0,
        obstacles=[(12.0, 8.0, 2.0, 2.0)]
    )
    
    # Test different overlap penalty values
    penalty_values = [100.0, 500.0, 1000.0, 2000.0]
    results = {}
    
    for penalty in penalty_values:
        logger.info(f"Testing overlap penalty: {penalty}")
        
        # Create config with modified penalty
        config = base_config
        config.qubo_weights.overlap_penalty = penalty
        
        # Run optimization
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        formulator = QUBOFormulator(modules, config)
        solver = QuantumAnnealer(config)
        
        # Run multiple times for statistics
        trial_results = []
        for _ in range(3):
            solution_result = solver.solve_with_formulator(formulator)
            postprocessor = SolutionPostProcessor(modules, config)
            processed_solution = postprocessor.process_solution(solution_result['solution'])
            trial_results.append(processed_solution)
        
        results[penalty] = trial_results
    
    # Analyze sensitivity
    analyzer = MetricsAnalyzer(base_config)
    fig = analyzer.plot_parameter_sensitivity(results, 'total_parking_spaces')
    plt.title("Sensitivity to Overlap Penalty Parameter")
    plt.show()
    
    logger.info("Parameter sensitivity analysis completed!")


if __name__ == "__main__":
    # Run basic optimization
    solution = run_basic_optimization()
    
    # Optionally run sensitivity analysis
    print("\nWould you like to run parameter sensitivity analysis? (y/n): ", end="")
    if input().lower().startswith('y'):
        demonstrate_parameter_sensitivity()
    
    print("\nBasic example completed!")
