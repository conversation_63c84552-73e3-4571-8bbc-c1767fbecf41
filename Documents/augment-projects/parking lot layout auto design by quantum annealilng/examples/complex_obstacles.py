#!/usr/bin/env python3
"""
Complex obstacles parking lot optimization example.

This example demonstrates optimization with multiple obstacles
and complex geometric constraints.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from src.core.config import ParkingLotConfig
from src.preprocessing.module_generator import ModuleGenerator
from src.preprocessing.obstacle_handler import ObstacleHandler
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer
from src.solver.postprocessor import SolutionPostProcessor
from src.visualization.layout_plotter import LayoutPlotter
from src.visualization.metrics_analyzer import MetricsAnalyzer
from src.utils.helpers import setup_logging, print_solution_summary, timer
import matplotlib.pyplot as plt
import numpy as np


@timer
def run_complex_obstacles_optimization():
    """Run optimization with complex obstacle configuration."""
    
    logger = setup_logging("INFO")
    logger.info("Starting complex obstacles optimization example")
    
    # Create a parking lot with multiple obstacles (simulating underground structure)
    obstacles = [
        # Main structural columns (larger)
        (12.0, 8.0, 3.0, 3.0),   # Column 1
        (35.0, 12.0, 3.0, 3.0),  # Column 2
        (20.0, 25.0, 3.0, 3.0),  # Column 3
        (45.0, 30.0, 3.0, 3.0),  # Column 4
        
        # Smaller support columns
        (8.0, 20.0, 1.5, 1.5),   # Support 1
        (28.0, 5.0, 1.5, 1.5),   # Support 2
        (42.0, 18.0, 1.5, 1.5),  # Support 3
        (15.0, 35.0, 1.5, 1.5),  # Support 4
        
        # Utility areas (rectangular)
        (2.0, 2.0, 4.0, 2.0),    # Utility room 1
        (50.0, 38.0, 6.0, 3.0),  # Utility room 2
        
        # Irregular obstacles
        (30.0, 32.0, 2.5, 4.0),  # Elevator shaft
        (38.0, 8.0, 2.0, 6.0)    # Stairwell
    ]
    
    config = ParkingLotConfig(
        width=60.0,  # Larger parking lot
        height=40.0,
        obstacles=obstacles
    )
    
    logger.info(f"Parking lot: {config.width}m x {config.height}m")
    logger.info(f"Obstacles: {len(config.obstacles)}")
    
    # Analyze obstacles
    obstacle_handler = ObstacleHandler(config)
    obstacle_stats = obstacle_handler.get_obstacle_statistics()
    validation_issues = obstacle_handler.validate_obstacle_configuration()
    
    logger.info(f"Total obstacle area: {obstacle_stats['total_area']:.2f} m²")
    logger.info(f"Obstacle coverage: {obstacle_stats['coverage_ratio']:.1%}")
    
    if validation_issues:
        logger.warning("Obstacle validation issues:")
        for issue in validation_issues:
            logger.warning(f"  - {issue}")
    
    # Generate candidate modules
    logger.info("Generating candidate road modules...")
    generator = ModuleGenerator(config)
    modules = generator.generate_candidate_modules()
    
    module_stats = generator.get_module_statistics()
    logger.info(f"Generated {module_stats['total_modules']} candidate modules")
    
    # Filter modules to reduce problem size if needed
    if module_stats['total_modules'] > 200:
        logger.info("Filtering modules to reduce problem complexity...")
        filtered_modules = generator.filter_modules_by_criteria(
            min_accessibility=2.0,  # Only modules serving at least 2 parking spaces
            module_types=['single_lane', 'double_lane']  # Exclude turning areas for now
        )
        logger.info(f"Filtered to {len(filtered_modules)} modules")
        modules = filtered_modules
    
    # Formulate QUBO problem
    logger.info("Formulating QUBO problem...")
    formulator = QUBOFormulator(modules, config)
    qubo_analysis = formulator.analyze_qubo_structure()
    
    logger.info(f"QUBO matrix: {qubo_analysis['num_variables']} variables")
    logger.info(f"Matrix density: {qubo_analysis['matrix_density']:.3f}")
    
    # Check for potential issues
    validation_issues = formulator.validate_qubo_formulation()
    if validation_issues:
        logger.warning("QUBO formulation issues:")
        for issue in validation_issues:
            logger.warning(f"  - {issue}")
    
    # Solve using quantum annealing with increased reads for complex problem
    logger.info("Solving with quantum annealing...")
    solver = QuantumAnnealer(config)
    
    # Increase number of reads for better solution quality
    solution_result = solver.solve_with_formulator(
        formulator, 
        num_reads=2000  # More reads for complex problem
    )
    
    logger.info(f"Best energy: {solution_result['energy']:.2f}")
    
    # Analyze solution quality
    quality_analysis = solver.analyze_solution_quality()
    logger.info(f"Success rate: {quality_analysis['success_rate']:.1%}")
    logger.info(f"Unique solutions: {quality_analysis['unique_solutions']}")
    
    # Post-process solution
    logger.info("Post-processing solution...")
    postprocessor = SolutionPostProcessor(modules, config)
    processed_solution = postprocessor.process_solution(solution_result['solution'])
    
    # Print detailed summary
    print_solution_summary(processed_solution)
    
    # Additional analysis for complex case
    network_analysis = processed_solution['network_analysis']
    logger.info(f"Connected components: {network_analysis['num_components']}")
    logger.info(f"Largest component size: {network_analysis['largest_component_size']}")
    
    # Create comprehensive visualizations
    logger.info("Creating visualizations...")
    plotter = LayoutPlotter(config)
    
    # Main solution plot
    fig1, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # Plot 1: Base parking lot with obstacles
    plotter.plot_parking_lot_base(ax=axes[0,0])
    axes[0,0].set_title("Parking Lot with Obstacles")
    
    # Plot 2: Candidate modules (sample)
    sample_modules = modules[:50] if len(modules) > 50 else modules
    plotter.plot_candidate_modules(sample_modules, ax=axes[0,1])
    axes[0,1].set_title("Sample Candidate Modules")
    
    # Plot 3: Final solution
    plotter.plot_solution(
        processed_solution['selected_modules'],
        all_modules=modules,
        ax=axes[1,0],
        show_accessibility=True
    )
    axes[1,0].set_title("Optimized Layout")
    
    # Plot 4: Module accessibility heatmap
    plotter.plot_module_heatmap(
        modules, 
        value_key='parking_accessibility_value',
        ax=axes[1,1]
    )
    axes[1,1].set_title("Parking Accessibility Heatmap")
    
    plt.tight_layout()
    
    # Performance metrics analysis
    analyzer = MetricsAnalyzer(config)
    fig2 = analyzer.plot_solution_metrics([processed_solution])
    
    # QUBO analysis
    fig3 = analyzer.plot_qubo_analysis(qubo_analysis)
    
    # Show all plots
    plt.show()
    
    # Save results
    plotter.save_plot(fig1, "complex_obstacles_analysis.png")
    plotter.save_plot(fig2, "complex_obstacles_metrics.png")
    plotter.save_plot(fig3, "complex_obstacles_qubo.png")
    
    logger.info("Complex obstacles optimization completed successfully!")
    
    return processed_solution


def compare_different_configurations():
    """Compare optimization results for different obstacle configurations."""
    
    logger = setup_logging("INFO")
    logger.info("Comparing different obstacle configurations...")
    
    # Define different configurations
    configurations = [
        {
            'name': 'Minimal Obstacles',
            'obstacles': [(20.0, 15.0, 2.0, 2.0)]
        },
        {
            'name': 'Moderate Obstacles', 
            'obstacles': [
                (15.0, 10.0, 2.0, 2.0),
                (35.0, 20.0, 2.0, 2.0),
                (25.0, 5.0, 1.5, 1.5)
            ]
        },
        {
            'name': 'Heavy Obstacles',
            'obstacles': [
                (10.0, 8.0, 3.0, 3.0),
                (30.0, 12.0, 3.0, 3.0),
                (20.0, 25.0, 3.0, 3.0),
                (8.0, 20.0, 2.0, 2.0),
                (35.0, 5.0, 2.0, 2.0),
                (40.0, 28.0, 2.0, 2.0)
            ]
        }
    ]
    
    solutions = []
    solution_labels = []
    
    for config_def in configurations:
        logger.info(f"Testing configuration: {config_def['name']}")
        
        config = ParkingLotConfig(
            width=50.0,
            height=35.0,
            obstacles=config_def['obstacles']
        )
        
        # Run optimization
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        formulator = QUBOFormulator(modules, config)
        solver = QuantumAnnealer(config)
        solution_result = solver.solve_with_formulator(formulator)
        
        postprocessor = SolutionPostProcessor(modules, config)
        processed_solution = postprocessor.process_solution(solution_result['solution'])
        
        solutions.append(processed_solution)
        solution_labels.append(config_def['name'])
    
    # Compare solutions
    plotter = LayoutPlotter(configurations[0])  # Use first config for plotter
    fig1 = plotter.plot_solution_comparison(solutions, solution_labels)
    
    analyzer = MetricsAnalyzer(configurations[0])
    fig2 = analyzer.plot_solution_comparison_radar(solutions, solution_labels)
    
    # Ranking analysis
    comparison_result = SolutionPostProcessor([], configurations[0]).compare_solutions(solutions)
    
    logger.info("Solution ranking:")
    for i, ranked_solution in enumerate(comparison_result['ranked_solutions']):
        solution_idx = ranked_solution['solution_index']
        score = ranked_solution['composite_score']
        logger.info(f"{i+1}. {solution_labels[solution_idx]} (score: {score:.3f})")
    
    plt.show()
    
    logger.info("Configuration comparison completed!")


if __name__ == "__main__":
    # Run complex obstacles optimization
    solution = run_complex_obstacles_optimization()
    
    # Optionally run configuration comparison
    print("\nWould you like to compare different obstacle configurations? (y/n): ", end="")
    if input().lower().startswith('y'):
        compare_different_configurations()
    
    print("\nComplex obstacles example completed!")
