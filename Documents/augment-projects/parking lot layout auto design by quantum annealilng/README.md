# Quantum Annealing Parking Lot Layout Optimization

A quantum annealing-based solution for optimizing underground parking lot layouts to maximize parking spaces while accommodating structural constraints using a module-based QUBO approach.

## Overview

This project implements a sophisticated optimization algorithm that:
- Uses quantum annealing to solve parking lot layout optimization
- Employs a module-based QUBO (Quadratic Unconstrained Binary Optimization) approach
- Handles structural obstacles like columns and walls
- Maximizes parking space utilization while ensuring proper road connectivity

## Key Features

- **Module-Based Modeling**: Defines variables for road modules rather than individual cells
- **Obstacle Handling**: Accounts for structural columns and other constraints
- **Quantum Annealing**: Leverages D-Wave quantum computers or simulators
- **Visualization Tools**: Comprehensive layout visualization and analysis
- **Performance Metrics**: Detailed analysis of space utilization and connectivity

## Project Structure

```
├── src/
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py          # Configuration and constants
│   │   ├── geometry.py        # Geometric utilities and car dimensions
│   │   └── grid.py           # Grid cell and coordinate management
│   ├── preprocessing/
│   │   ├── __init__.py
│   │   ├── module_generator.py # Candidate module generation
│   │   └── obstacle_handler.py # Obstacle detection and filtering
│   ├── qubo/
│   │   ├── __init__.py
│   │   ├── formulation.py     # QUBO model formulation
│   │   └── constraints.py     # Constraint definitions
│   ├── solver/
│   │   ├── __init__.py
│   │   ├── quantum_annealer.py # Quantum annealing interface
│   │   └── postprocessor.py   # Result processing
│   ├── visualization/
│   │   ├── __init__.py
│   │   ├── layout_plotter.py  # Layout visualization
│   │   └── metrics_analyzer.py # Performance analysis
│   └── utils/
│       ├── __init__.py
│       └── helpers.py         # Utility functions
├── tests/
│   ├── __init__.py
│   ├── test_preprocessing.py
│   ├── test_qubo.py
│   ├── test_solver.py
│   └── test_integration.py
├── examples/
│   ├── basic_layout.py
│   ├── complex_obstacles.py
│   └── performance_comparison.py
├── config/
│   └── default_config.yaml
├── requirements.txt
└── README.md
```

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

```python
from src.core.config import ParkingLotConfig
from src.preprocessing.module_generator import ModuleGenerator
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer

# Configure parking lot
config = ParkingLotConfig(
    width=50.0,  # meters
    height=30.0,  # meters
    grid_size=0.5,  # meters
    obstacles=[(10, 10, 2, 2)]  # x, y, width, height
)

# Generate candidate modules
generator = ModuleGenerator(config)
modules = generator.generate_candidate_modules()

# Formulate QUBO problem
formulator = QUBOFormulator(modules, config)
qubo_matrix = formulator.build_qubo_matrix()

# Solve using quantum annealing
solver = QuantumAnnealer()
solution = solver.solve(qubo_matrix)

# Visualize results
from src.visualization.layout_plotter import LayoutPlotter
plotter = LayoutPlotter(config)
plotter.plot_solution(solution, modules)
```

## Algorithm Overview

### Phase 1: Preprocessing and Module Generation
- Define standard car dimensions and grid cells
- Calculate valid module specifications for roads
- Generate candidate module pool
- Filter modules that overlap with obstacles

### Phase 2: QUBO Model Formulation
- Define binary variables for each candidate module
- Construct objective function with multiple components:
  - Overlap constraints (hard penalty)
  - Coverage optimization (maximize parking accessibility)
  - Connectivity rewards (ensure road network)
  - Area efficiency (minimize road area)

### Phase 3: Quantum Annealing Integration
- Build QUBO matrix from objective function
- Interface with D-Wave quantum annealer or simulator
- Post-process results to extract final layout
- Validate constraint satisfaction

## Configuration

Key parameters can be adjusted in `config/default_config.yaml`:
- Car dimensions and spacing requirements
- Module size specifications
- QUBO weight coefficients
- Solver parameters

## Testing

Run the test suite:
```bash
pytest tests/ -v --cov=src
```

## Contributing

1. Follow PEP 8 style guidelines
2. Add tests for new functionality
3. Update documentation as needed
4. Use type hints where appropriate

## License

MIT License - see LICENSE file for details
