"""
Tests for preprocessing modules.

This module tests the module generation and obstacle handling functionality.
"""

import pytest
import numpy as np
from src.core.config import ParkingLotConfig
from src.core.geometry import Rectangle, Point
from src.preprocessing.module_generator import ModuleGenerator, RoadModule
from src.preprocessing.obstacle_handler import ObstacleHandler


class TestModuleGenerator:
    """Test cases for ModuleGenerator class."""
    
    def setup_method(self):
        """Set up test configuration."""
        self.config = ParkingLotConfig(
            width=30.0,
            height=20.0,
            obstacles=[(10.0, 8.0, 2.0, 2.0)]
        )
        self.generator = ModuleGenerator(self.config)
    
    def test_module_generation(self):
        """Test basic module generation."""
        modules = self.generator.generate_candidate_modules()
        
        assert len(modules) > 0, "Should generate at least some modules"
        assert all(isinstance(m, RoadModule) for m in modules), "All items should be RoadModule instances"
        assert all(m.id >= 0 for m in modules), "All modules should have valid IDs"
    
    def test_module_types(self):
        """Test that different module types are generated."""
        modules = self.generator.generate_candidate_modules()
        
        module_types = {m.module_type for m in modules}
        expected_types = {'single_lane', 'double_lane', 'turning_area'}
        
        assert len(module_types.intersection(expected_types)) > 0, "Should generate multiple module types"
    
    def test_module_orientations(self):
        """Test that different orientations are generated."""
        modules = self.generator.generate_candidate_modules()
        
        orientations = {m.orientation for m in modules}
        expected_orientations = {'horizontal', 'vertical', 'square'}
        
        assert len(orientations.intersection(expected_orientations)) > 0, "Should generate multiple orientations"
    
    def test_obstacle_avoidance(self):
        """Test that modules don't overlap with obstacles."""
        modules = self.generator.generate_candidate_modules()
        obstacle_rect = Rectangle(10.0, 8.0, 2.0, 2.0)
        
        for module in modules:
            assert not module.rectangle.overlaps_with(obstacle_rect), \
                f"Module {module.id} overlaps with obstacle"
    
    def test_module_statistics(self):
        """Test module statistics calculation."""
        modules = self.generator.generate_candidate_modules()
        stats = self.generator.get_module_statistics()
        
        assert stats['total_modules'] == len(modules)
        assert stats['total_modules'] > 0
        assert 'single_lane_modules' in stats
        assert 'double_lane_modules' in stats
        assert 'turning_area_modules' in stats
        assert stats['average_accessibility'] >= 0
    
    def test_module_filtering(self):
        """Test module filtering functionality."""
        modules = self.generator.generate_candidate_modules()
        
        # Filter by accessibility
        filtered = self.generator.filter_modules_by_criteria(min_accessibility=1.0)
        assert len(filtered) <= len(modules)
        assert all(m.parking_accessibility_value >= 1.0 for m in filtered)
        
        # Filter by type
        filtered_types = self.generator.filter_modules_by_criteria(
            module_types=['single_lane']
        )
        assert all(m.module_type == 'single_lane' for m in filtered_types)
    
    def test_adjacency_calculation(self):
        """Test adjacency calculation between modules."""
        modules = self.generator.generate_candidate_modules()
        
        # Find modules that should be adjacent
        adjacent_pairs = []
        for i, module1 in enumerate(modules):
            for j, module2 in enumerate(modules):
                if i < j and len(module1.adjacent_modules.intersection({module2.id})) > 0:
                    adjacent_pairs.append((module1, module2))
        
        # Verify adjacency makes sense geometrically
        for module1, module2 in adjacent_pairs[:5]:  # Test first 5 pairs
            # Adjacent modules should be close but not overlapping
            assert not module1.rectangle.overlaps_with(module2.rectangle)
            
            # Calculate minimum distance between rectangles
            min_distance = float('inf')
            for corner1 in module1.rectangle.corners:
                for corner2 in module2.rectangle.corners:
                    distance = corner1.distance_to(corner2)
                    min_distance = min(min_distance, distance)
            
            # Adjacent modules should be reasonably close
            assert min_distance < 10.0, "Adjacent modules should be close"


class TestObstacleHandler:
    """Test cases for ObstacleHandler class."""
    
    def setup_method(self):
        """Set up test configuration."""
        self.config = ParkingLotConfig(
            width=40.0,
            height=25.0,
            obstacles=[
                (10.0, 8.0, 3.0, 3.0),
                (25.0, 15.0, 2.0, 2.0)
            ]
        )
        self.handler = ObstacleHandler(self.config)
    
    def test_obstacle_creation(self):
        """Test obstacle rectangle creation."""
        assert len(self.handler.obstacles) == 2
        assert all(isinstance(obs, Rectangle) for obs in self.handler.obstacles)
    
    def test_overlap_detection(self):
        """Test module-obstacle overlap detection."""
        # Create a module that overlaps with first obstacle
        overlapping_rect = Rectangle(9.0, 7.0, 4.0, 4.0)
        assert self.handler.check_module_obstacle_overlap(overlapping_rect)
        
        # Create a module that doesn't overlap
        non_overlapping_rect = Rectangle(0.0, 0.0, 5.0, 5.0)
        assert not self.handler.check_module_obstacle_overlap(non_overlapping_rect)
    
    def test_overlap_area_calculation(self):
        """Test overlap area calculation."""
        # Module that partially overlaps
        partial_overlap_rect = Rectangle(11.0, 9.0, 4.0, 4.0)
        overlap_area = self.handler.get_obstacle_overlap_area(partial_overlap_rect)
        assert overlap_area > 0
        assert overlap_area < partial_overlap_rect.area
        
        # Module that doesn't overlap
        no_overlap_rect = Rectangle(0.0, 0.0, 5.0, 5.0)
        assert self.handler.get_obstacle_overlap_area(no_overlap_rect) == 0
    
    def test_clearance_calculation(self):
        """Test clearance distance calculation."""
        # Point near obstacle
        near_point = Point(8.0, 8.0)
        clearance = self.handler.calculate_clearance_distances(near_point)
        
        assert clearance['min_distance'] >= 0
        assert clearance['nearest_obstacle_id'] is not None
        assert isinstance(clearance['clearance_sufficient'], bool)
        
        # Point far from obstacles
        far_point = Point(0.0, 0.0)
        far_clearance = self.handler.calculate_clearance_distances(far_point)
        assert far_clearance['min_distance'] > clearance['min_distance']
    
    def test_obstacle_validation(self):
        """Test obstacle configuration validation."""
        issues = self.handler.validate_obstacle_configuration()
        
        # Should not have issues with valid configuration
        assert isinstance(issues, list)
        
        # Test with invalid configuration
        invalid_config = ParkingLotConfig(
            width=10.0,
            height=10.0,
            obstacles=[(0.0, 0.0, 15.0, 15.0)]  # Obstacle larger than parking lot
        )
        invalid_handler = ObstacleHandler(invalid_config)
        invalid_issues = invalid_handler.validate_obstacle_configuration()
        assert len(invalid_issues) > 0
    
    def test_obstacle_statistics(self):
        """Test obstacle statistics calculation."""
        stats = self.handler.get_obstacle_statistics()
        
        assert stats['num_obstacles'] == 2
        assert stats['total_area'] > 0
        assert 0 <= stats['coverage_ratio'] <= 1
        assert stats['average_area'] > 0
        assert len(stats['obstacle_positions']) == 2
        assert len(stats['obstacle_dimensions']) == 2
    
    def test_free_region_finding(self):
        """Test finding obstacle-free regions."""
        free_regions = self.handler.find_obstacle_free_regions()
        
        assert isinstance(free_regions, list)
        # Should find at least some free regions
        assert len(free_regions) >= 0
        
        # All free regions should be within parking lot bounds
        parking_lot_bounds = Rectangle(0, 0, self.config.width, self.config.height)
        for region in free_regions:
            assert region.left >= parking_lot_bounds.left
            assert region.right <= parking_lot_bounds.right
            assert region.bottom >= parking_lot_bounds.bottom
            assert region.top <= parking_lot_bounds.top


class TestIntegration:
    """Integration tests for preprocessing components."""
    
    def test_module_generation_with_complex_obstacles(self):
        """Test module generation with complex obstacle configuration."""
        config = ParkingLotConfig(
            width=50.0,
            height=30.0,
            obstacles=[
                (10.0, 10.0, 3.0, 3.0),
                (25.0, 15.0, 2.0, 2.0),
                (40.0, 8.0, 2.5, 2.5),
                (15.0, 25.0, 1.5, 1.5)
            ]
        )
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        # Should generate modules despite complex obstacles
        assert len(modules) > 0
        
        # Verify no modules overlap with obstacles
        handler = ObstacleHandler(config)
        for module in modules:
            assert not handler.check_module_obstacle_overlap(module.rectangle)
    
    def test_empty_parking_lot(self):
        """Test behavior with no obstacles."""
        config = ParkingLotConfig(
            width=20.0,
            height=15.0,
            obstacles=[]
        )
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        # Should generate many modules without obstacles
        assert len(modules) > 0
        
        handler = ObstacleHandler(config)
        stats = handler.get_obstacle_statistics()
        assert stats['num_obstacles'] == 0
        assert stats['coverage_ratio'] == 0
    
    def test_heavily_obstructed_parking_lot(self):
        """Test behavior with many obstacles."""
        # Create a parking lot with many obstacles
        obstacles = []
        for x in range(5, 45, 8):
            for y in range(5, 25, 8):
                obstacles.append((x, y, 2.0, 2.0))
        
        config = ParkingLotConfig(
            width=50.0,
            height=30.0,
            obstacles=obstacles
        )
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        # Should still generate some modules
        assert len(modules) >= 0
        
        # Verify obstacle handling
        handler = ObstacleHandler(config)
        stats = handler.get_obstacle_statistics()
        assert stats['num_obstacles'] == len(obstacles)
        assert stats['coverage_ratio'] > 0


if __name__ == "__main__":
    pytest.main([__file__])
