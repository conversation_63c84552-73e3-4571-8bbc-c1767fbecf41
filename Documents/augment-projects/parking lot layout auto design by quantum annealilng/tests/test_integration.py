"""
Integration tests for the complete parking lot optimization system.

This module tests the end-to-end functionality of the quantum annealing
parking lot optimization system.
"""

import pytest
import numpy as np
from src.core.config import ParkingLotConfig
from src.core.geometry import Rectangle
from src.preprocessing.module_generator import ModuleGenerator
from src.qubo.formulation import QUBOFormulator
from src.solver.quantum_annealer import QuantumAnnealer
from src.solver.postprocessor import SolutionPostProcessor
from src.visualization.layout_plotter import LayoutPlotter
from src.visualization.metrics_analyzer import MetricsAnalyzer
from src.utils.helpers import create_test_config, validate_config


class TestEndToEndOptimization:
    """Test complete optimization pipeline."""
    
    def setup_method(self):
        """Set up test configuration."""
        self.config = create_test_config(width=25.0, height=20.0)
    
    def test_basic_optimization_pipeline(self):
        """Test the complete optimization pipeline."""
        # Validate configuration
        errors = validate_config(self.config)
        assert len(errors) == 0, f"Configuration validation failed: {errors}"
        
        # Generate modules
        generator = ModuleGenerator(self.config)
        modules = generator.generate_candidate_modules()
        assert len(modules) > 0, "Should generate candidate modules"
        
        # Formulate QUBO
        formulator = QUBOFormulator(modules, self.config)
        qubo_matrix = formulator.build_qubo_matrix()
        assert len(qubo_matrix) > 0, "Should create QUBO matrix"
        
        # Solve with quantum annealing
        solver = QuantumAnnealer(self.config)
        solution_result = solver.solve(qubo_matrix)
        assert 'solution' in solution_result, "Should return solution"
        assert 'energy' in solution_result, "Should return energy"
        
        # Post-process solution
        postprocessor = SolutionPostProcessor(modules, self.config)
        processed_solution = postprocessor.process_solution(solution_result['solution'])
        
        # Verify solution structure
        assert 'selected_modules' in processed_solution
        assert 'validation' in processed_solution
        assert 'performance_metrics' in processed_solution
        assert 'solution_summary' in processed_solution
        
        # Basic solution validation
        validation = processed_solution['validation']
        assert isinstance(validation['is_valid'], bool)
        assert validation['num_connected_components'] >= 0
        
        metrics = processed_solution['performance_metrics']
        assert metrics['total_road_area'] >= 0
        assert metrics['total_parking_spaces'] >= 0
    
    def test_optimization_with_different_sizes(self):
        """Test optimization with different parking lot sizes."""
        sizes = [
            (15.0, 10.0),  # Small
            (30.0, 20.0),  # Medium
            (50.0, 35.0)   # Large
        ]
        
        for width, height in sizes:
            config = create_test_config(width=width, height=height)
            
            # Run optimization
            generator = ModuleGenerator(config)
            modules = generator.generate_candidate_modules()
            
            if len(modules) == 0:
                continue  # Skip if no modules generated
            
            formulator = QUBOFormulator(modules, config)
            solver = QuantumAnnealer(config)
            
            solution_result = solver.solve_with_formulator(formulator)
            
            postprocessor = SolutionPostProcessor(modules, config)
            processed_solution = postprocessor.process_solution(solution_result['solution'])
            
            # Verify solution makes sense for the size
            metrics = processed_solution['performance_metrics']
            parking_lot_area = width * height
            
            assert metrics['total_road_area'] <= parking_lot_area
            assert 0 <= metrics['space_utilization'] <= 1
    
    def test_optimization_with_no_obstacles(self):
        """Test optimization with no obstacles."""
        config = ParkingLotConfig(
            width=30.0,
            height=20.0,
            obstacles=[]
        )
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        # Should generate many modules without obstacles
        assert len(modules) > 10
        
        # Run optimization
        formulator = QUBOFormulator(modules, config)
        solver = QuantumAnnealer(config)
        solution_result = solver.solve_with_formulator(formulator)
        
        postprocessor = SolutionPostProcessor(modules, config)
        processed_solution = postprocessor.process_solution(solution_result['solution'])
        
        # Should find a valid solution
        assert processed_solution['validation']['is_valid']
        assert len(processed_solution['selected_modules']) > 0
    
    def test_optimization_with_many_obstacles(self):
        """Test optimization with many obstacles."""
        # Create many small obstacles
        obstacles = []
        for x in range(5, 25, 5):
            for y in range(5, 15, 5):
                obstacles.append((x, y, 1.0, 1.0))
        
        config = ParkingLotConfig(
            width=30.0,
            height=20.0,
            obstacles=obstacles
        )
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        if len(modules) == 0:
            pytest.skip("No modules generated with heavy obstacles")
        
        # Run optimization
        formulator = QUBOFormulator(modules, config)
        solver = QuantumAnnealer(config)
        solution_result = solver.solve_with_formulator(formulator)
        
        postprocessor = SolutionPostProcessor(modules, config)
        processed_solution = postprocessor.process_solution(solution_result['solution'])
        
        # Solution should respect obstacles
        for module in processed_solution['selected_modules']:
            for obs_x, obs_y, obs_w, obs_h in obstacles:
                obs_rect = Rectangle(obs_x, obs_y, obs_w, obs_h)
                assert not module.rectangle.overlaps_with(obs_rect)


class TestSolutionQuality:
    """Test solution quality and consistency."""
    
    def test_solution_consistency(self):
        """Test that multiple runs produce consistent results."""
        config = create_test_config()
        
        solutions = []
        for _ in range(3):
            generator = ModuleGenerator(config)
            modules = generator.generate_candidate_modules()
            
            formulator = QUBOFormulator(modules, config)
            solver = QuantumAnnealer(config)
            solution_result = solver.solve_with_formulator(formulator)
            
            postprocessor = SolutionPostProcessor(modules, config)
            processed_solution = postprocessor.process_solution(solution_result['solution'])
            solutions.append(processed_solution)
        
        # Compare solution quality
        energies = [sol.get('energy', float('inf')) for sol in solutions]
        parking_spaces = [sol['performance_metrics']['total_parking_spaces'] for sol in solutions]
        
        # Solutions should be reasonably consistent
        energy_std = np.std(energies)
        spaces_std = np.std(parking_spaces)
        
        # Allow some variation but not too much
        assert energy_std < abs(np.mean(energies)) * 0.5  # Within 50% of mean
        assert spaces_std < np.mean(parking_spaces) * 0.3  # Within 30% of mean
    
    def test_parameter_sensitivity(self):
        """Test sensitivity to QUBO parameters."""
        base_config = create_test_config()
        
        # Test different overlap penalties
        penalties = [500.0, 1000.0, 2000.0]
        results = []
        
        for penalty in penalties:
            config = base_config
            config.qubo_weights.overlap_penalty = penalty
            
            generator = ModuleGenerator(config)
            modules = generator.generate_candidate_modules()
            
            if len(modules) == 0:
                continue
            
            formulator = QUBOFormulator(modules, config)
            solver = QuantumAnnealer(config)
            solution_result = solver.solve_with_formulator(formulator)
            
            postprocessor = SolutionPostProcessor(modules, config)
            processed_solution = postprocessor.process_solution(solution_result['solution'])
            
            results.append(processed_solution)
        
        if len(results) >= 2:
            # Higher penalty should lead to fewer overlaps
            overlaps = [len(sol['validation'].get('overlap_violations', [])) for sol in results]
            # Generally, higher penalties should reduce overlaps
            assert max(overlaps) >= min(overlaps)
    
    def test_solution_validation(self):
        """Test comprehensive solution validation."""
        config = create_test_config()
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        if len(modules) == 0:
            pytest.skip("No modules generated")
        
        formulator = QUBOFormulator(modules, config)
        solver = QuantumAnnealer(config)
        solution_result = solver.solve_with_formulator(formulator)
        
        postprocessor = SolutionPostProcessor(modules, config)
        processed_solution = postprocessor.process_solution(solution_result['solution'])
        
        # Detailed validation
        validation = processed_solution['validation']
        selected_modules = processed_solution['selected_modules']
        
        # Check for overlaps manually
        actual_overlaps = 0
        for i, mod1 in enumerate(selected_modules):
            for j, mod2 in enumerate(selected_modules):
                if i < j and mod1.rectangle.overlaps_with(mod2.rectangle):
                    actual_overlaps += 1
        
        reported_overlaps = len(validation.get('overlap_violations', []))
        assert actual_overlaps == reported_overlaps, "Overlap count mismatch"
        
        # Check area constraints
        total_area = sum(mod.area for mod in selected_modules)
        parking_lot_area = config.width * config.height
        assert total_area <= parking_lot_area, "Total module area exceeds parking lot"
        
        # Check that modules are within bounds
        for module in selected_modules:
            rect = module.rectangle
            assert rect.left >= 0 and rect.right <= config.width
            assert rect.bottom >= 0 and rect.top <= config.height


class TestVisualization:
    """Test visualization components."""
    
    def test_layout_plotting(self):
        """Test layout plotting functionality."""
        config = create_test_config()
        
        generator = ModuleGenerator(config)
        modules = generator.generate_candidate_modules()
        
        if len(modules) == 0:
            pytest.skip("No modules generated")
        
        # Test plotter creation
        plotter = LayoutPlotter(config)
        
        # Test basic plotting (should not raise exceptions)
        import matplotlib.pyplot as plt
        
        fig, ax = plt.subplots()
        plotter.plot_parking_lot_base(ax=ax)
        plt.close(fig)
        
        # Test module plotting
        fig, ax = plt.subplots()
        sample_modules = modules[:10] if len(modules) > 10 else modules
        plotter.plot_candidate_modules(sample_modules, ax=ax)
        plt.close(fig)
    
    def test_metrics_analysis(self):
        """Test metrics analysis functionality."""
        config = create_test_config()
        
        # Create a dummy solution for testing
        dummy_solution = {
            'performance_metrics': {
                'total_parking_spaces': 20,
                'total_road_area': 100.0,
                'area_efficiency': 0.2,
                'connectivity_ratio': 0.8,
                'space_utilization': 0.15
            },
            'validation': {
                'is_valid': True,
                'overlap_violations': [],
                'num_connected_components': 1
            }
        }
        
        analyzer = MetricsAnalyzer(config)
        
        # Test metrics plotting (should not raise exceptions)
        import matplotlib.pyplot as plt
        
        fig = analyzer.plot_solution_metrics([dummy_solution])
        plt.close(fig)
        
        # Test report generation
        report = analyzer.generate_performance_report(dummy_solution)
        assert isinstance(report, str)
        assert len(report) > 0


class TestErrorHandling:
    """Test error handling and edge cases."""
    
    def test_invalid_configuration(self):
        """Test handling of invalid configurations."""
        # Test negative dimensions
        with pytest.raises((ValueError, AssertionError)):
            config = ParkingLotConfig(width=-10.0, height=20.0)
            errors = validate_config(config)
            if errors:
                raise ValueError("Invalid configuration")
    
    def test_empty_module_list(self):
        """Test handling of empty module lists."""
        config = create_test_config()
        empty_modules = []
        
        # QUBO formulation with empty modules
        formulator = QUBOFormulator(empty_modules, config)
        qubo_matrix = formulator.build_qubo_matrix()
        assert len(qubo_matrix) == 0
        
        # Post-processing with empty solution
        postprocessor = SolutionPostProcessor(empty_modules, config)
        empty_solution = {}
        processed = postprocessor.process_solution(empty_solution)
        
        assert len(processed['selected_modules']) == 0
        assert processed['performance_metrics']['total_parking_spaces'] == 0
    
    def test_solver_failure_handling(self):
        """Test handling of solver failures."""
        config = create_test_config()
        
        # Test with invalid QUBO matrix
        solver = QuantumAnnealer(config)
        
        # Empty QUBO should be handled gracefully
        try:
            result = solver.solve({})
            # Should either succeed with empty result or raise appropriate error
            assert isinstance(result, dict)
        except (ValueError, RuntimeError):
            # Acceptable to raise these errors for invalid input
            pass


if __name__ == "__main__":
    pytest.main([__file__])
