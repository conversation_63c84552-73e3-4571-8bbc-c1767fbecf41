# Parking Lot Layout Optimization Configuration

# Parking Lot Dimensions and Obstacles
parking_lot:
  width: 50.0  # meters
  height: 30.0  # meters
  obstacles:  # List of [x, y, width, height] for structural columns/obstacles
    - [10.0, 10.0, 2.0, 2.0]  # Column 1
    - [25.0, 15.0, 1.5, 1.5]  # Column 2
    - [40.0, 8.0, 2.0, 2.0]   # Column 3

# Car and Parking Specifications
car:
  width: 2.5  # meters
  length: 5.0  # meters
  clearance: 0.3  # additional clearance around car

# Grid Configuration
grid:
  cell_size: 0.5  # meters per grid cell
  
# Road Module Specifications
road_modules:
  single_lane:
    min_width: 2.5  # meters
    max_width: 3.5  # meters
  double_lane:
    min_width: 5.0  # meters
    max_width: 6.0  # meters
  turning_area:
    min_size: 5.0  # meters (square)
  
# Module Generation Parameters
module_generation:
  min_module_length: 5.0  # meters
  max_module_length: 20.0  # meters
  step_size: 0.5  # meters (grid cell size)
  
# QUBO Model Weights
qubo_weights:
  overlap_penalty: 1000.0  # P_overlap - heavy penalty for overlapping modules
  coverage_reward: 10.0    # A - reward for parking accessibility
  connectivity_reward: 5.0  # B - reward for adjacent modules
  area_penalty: 1.0        # C - penalty for road area

# Quantum Annealing Parameters
quantum_annealing:
  num_reads: 1000
  annealing_time: 20  # microseconds
  chain_strength: 1.0
  auto_scale: true
  
# Solver Configuration
solver:
  use_simulator: true  # Set to false to use actual D-Wave hardware
  simulator_type: "neal"  # Options: "neal", "exact"
  
# Visualization Settings
visualization:
  figure_size: [12, 8]
  dpi: 300
  colors:
    parking_space: "#E8F4FD"
    road: "#666666"
    obstacle: "#FF6B6B"
    selected_module: "#4ECDC4"
    grid_lines: "#CCCCCC"
    
# Performance Metrics
metrics:
  calculate_connectivity: true
  calculate_accessibility: true
  calculate_efficiency: true
  
# Validation Parameters
validation:
  check_overlap: true
  check_connectivity: true
  min_parking_spaces: 10  # Minimum required parking spaces
  max_road_ratio: 0.4     # Maximum ratio of road area to total area
